'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  PostsTable,
  PostsFilters,
  PostPreview,
  BulkActions,
  ExportDialog,
} from '@/components/posts';
import { usePostsManagement } from '@/hooks/use-posts-management';
import {
  Plus,
  RefreshCw,
  Download,
  AlertTriangle,
  FileText,
  BarChart3,
} from 'lucide-react';
import type { PostWithRelations, ContentStatus } from '@/types';

export default function PostsPage() {
  const {
    posts,
    categories,
    authors,
    tags,
    pagination,
    loading,
    errors,
    filters,
    updateFilters,
    resetFilters,
    refresh,
    deletePost,
    duplicatePost,
    bulkStatusChange,
    bulkCategoryChange,
    bulkDelete,
  } = usePostsManagement();

  const [selectedPosts, setSelectedPosts] = useState<PostWithRelations[]>([]);
  const [previewPost, setPreviewPost] = useState<PostWithRelations | null>(
    null
  );
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Handle individual post actions
  const handleEdit = (post: PostWithRelations) => {
    window.location.href = `/posts/${post.id}/edit`;
  };

  const handleDelete = async (post: PostWithRelations) => {
    if (confirm(`Are you sure you want to delete "${post.title}"?`)) {
      await deletePost(post.id);
    }
  };

  const handleDuplicate = async (post: PostWithRelations) => {
    await duplicatePost(post.id);
  };

  const handlePreview = (post: PostWithRelations) => {
    setPreviewPost(post);
  };

  // Handle bulk actions
  const handleBulkAction = async (
    action: string,
    posts: PostWithRelations[]
  ) => {
    const postIds = posts.map((p) => p.id);

    switch (action) {
      case 'delete':
        if (confirm(`Are you sure you want to delete ${posts.length} posts?`)) {
          await bulkDelete(postIds);
          setSelectedPosts([]);
        }
        break;
      default:
        console.log('Unknown bulk action:', action);
    }
  };

  const handleBulkStatusChangeAction = async (
    posts: PostWithRelations[],
    newStatus: ContentStatus
  ) => {
    const postIds = posts.map((p) => p.id);
    await bulkStatusChange(postIds, newStatus);
    setSelectedPosts([]);
  };

  const handleBulkCategoryChangeAction = async (
    posts: PostWithRelations[],
    categoryId: string
  ) => {
    const postIds = posts.map((p) => p.id);
    await bulkCategoryChange(postIds, categoryId);
    setSelectedPosts([]);
  };

  const handleBulkDuplicate = async (posts: PostWithRelations[]) => {
    for (const post of posts) {
      await duplicatePost(post.id);
    }
    setSelectedPosts([]);
  };

  const handleBulkExport = async (
    posts: PostWithRelations[],
    format: 'csv' | 'json'
  ) => {
    // Simple export implementation
    const exportData = posts.map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      status: post.status,
      author: post.author?.full_name || 'Unknown',
      category: post.category?.name || 'Uncategorized',
      created_at: post.created_at,
      published_at: post.published_at,
      excerpt: post.excerpt,
    }));

    if (format === 'csv') {
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map((row) =>
          headers
            .map((header) =>
              JSON.stringify(row[header as keyof typeof row] || '')
            )
            .join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `posts-export-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    } else {
      const jsonContent = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `posts-export-${
        new Date().toISOString().split('T')[0]
      }.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const handleExport = async (options: any) => {
    // Advanced export implementation would go here
    console.log('Export options:', options);

    // For now, just export all posts as JSON
    await handleBulkExport(posts, 'json');
  };

  const getStatusCounts = () => {
    return posts.reduce((acc, post) => {
      acc[post.status] = (acc[post.status] || 0) + 1;
      return acc;
    }, {} as Record<ContentStatus, number>);
  };

  const statusCounts = getStatusCounts();
  const hasErrors = Object.values(errors).some(Boolean);
  const isLoading = Object.values(loading).some(Boolean);

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Posts Management
          </h1>
          <p className="text-muted-foreground">
            Manage your blog posts with advanced filtering, bulk operations, and
            analytics
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            {Object.entries(statusCounts).map(([status, count]) => (
              <Badge key={status} variant="outline" className="text-xs">
                {status}: {count}
              </Badge>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={refresh}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>

            <Button
              variant="outline"
              onClick={() => setShowExportDialog(true)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>

            <Button asChild>
              <a href="/posts/new" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Post
              </a>
            </Button>
          </div>
        </div>
      </div>

      {/* Error Messages */}
      {hasErrors && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Some data could not be loaded:</p>
              <ul className="list-disc list-inside text-sm">
                {Object.entries(errors).map(
                  ([key, error]) =>
                    error && (
                      <li key={key}>
                        {key}: {error}
                      </li>
                    )
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{posts.length}</div>
            <p className="text-xs text-muted-foreground">
              {pagination?.total || 0} total in database
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <BarChart3 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statusCounts.published || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {posts.length > 0
                ? Math.round(
                    ((statusCounts.published || 0) / posts.length) * 100
                  )
                : 0}
              % of visible posts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <FileText className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {statusCounts.draft || 0}
            </div>
            <p className="text-xs text-muted-foreground">Work in progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Review
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {statusCounts.pending_review || 0}
            </div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Filters */}
      <PostsFilters
        categories={categories}
        authors={authors}
        tags={tags}
        filters={filters}
        onFiltersChange={updateFilters}
        onReset={resetFilters}
      />

      {/* Bulk Actions */}
      {selectedPosts.length > 0 && (
        <BulkActions
          selectedPosts={selectedPosts}
          categories={categories}
          onBulkStatusChange={handleBulkStatusChangeAction}
          onBulkCategoryChange={handleBulkCategoryChangeAction}
          onBulkDelete={async (posts) => {
            const postIds = posts.map((p) => p.id);
            await bulkDelete(postIds);
            setSelectedPosts([]);
          }}
          onBulkDuplicate={handleBulkDuplicate}
          onBulkExport={handleBulkExport}
          onClearSelection={() => setSelectedPosts([])}
          isLoading={loading.actions}
        />
      )}

      {/* Posts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Posts
            <Badge variant="secondary">{posts.length}</Badge>
          </CardTitle>
          <CardDescription>
            Manage your blog posts with advanced filtering and bulk operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PostsTable
            posts={posts}
            categories={categories}
            authors={authors}
            isLoading={loading.posts}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onDuplicate={handleDuplicate}
            onPreview={handlePreview}
            onBulkAction={handleBulkAction}
          />
        </CardContent>
      </Card>

      {/* Post Preview Dialog */}
      {previewPost && (
        <PostPreview
          post={previewPost}
          isOpen={!!previewPost}
          onOpenChange={(open) => !open && setPreviewPost(null)}
        />
      )}

      {/* Export Dialog */}
      <ExportDialog
        posts={posts}
        isOpen={showExportDialog}
        onOpenChange={setShowExportDialog}
        onExport={handleExport}
      />
    </div>
  );
}
