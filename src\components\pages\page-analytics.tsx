'use client';

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  Clock, 
  Users, 
  MousePointer,
  BarChart3,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';

interface PageAnalyticsData {
  pageViews: number;
  uniqueVisitors: number;
  averageTimeOnPage: number;
  bounceRate: number;
  conversionRate: number;
  topReferrers: Array<{ source: string; visits: number; percentage: number }>;
  deviceBreakdown: Array<{ device: string; percentage: number; icon: any }>;
  performanceMetrics: {
    loadTime: number;
    seoScore: number;
    accessibilityScore: number;
    performanceScore: number;
  };
  trends: {
    pageViews: { current: number; previous: number; change: number };
    uniqueVisitors: { current: number; previous: number; change: number };
    bounceRate: { current: number; previous: number; change: number };
  };
}

interface PageAnalyticsProps {
  pageId: string;
  pageSlug: string;
  dateRange?: string;
}

// Mock data - in a real implementation, this would come from your analytics service
const mockAnalyticsData: PageAnalyticsData = {
  pageViews: 2847,
  uniqueVisitors: 1923,
  averageTimeOnPage: 142, // seconds
  bounceRate: 34.2,
  conversionRate: 3.8,
  topReferrers: [
    { source: 'Google Search', visits: 1245, percentage: 43.7 },
    { source: 'Direct', visits: 892, percentage: 31.3 },
    { source: 'Social Media', visits: 456, percentage: 16.0 },
    { source: 'Email', visits: 254, percentage: 8.9 },
  ],
  deviceBreakdown: [
    { device: 'Desktop', percentage: 52.3, icon: Monitor },
    { device: 'Mobile', percentage: 38.7, icon: Smartphone },
    { device: 'Tablet', percentage: 9.0, icon: Tablet },
  ],
  performanceMetrics: {
    loadTime: 1.2,
    seoScore: 85,
    accessibilityScore: 92,
    performanceScore: 78,
  },
  trends: {
    pageViews: { current: 2847, previous: 2156, change: 32.1 },
    uniqueVisitors: { current: 1923, previous: 1654, change: 16.3 },
    bounceRate: { current: 34.2, previous: 41.8, change: -18.2 },
  },
};

export function PageAnalytics({ pageId, pageSlug, dateRange = '30d' }: PageAnalyticsProps) {
  const data = mockAnalyticsData; // In real implementation, fetch based on pageId and dateRange

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getTrendIcon = (change: number) => {
    return change > 0 ? TrendingUp : TrendingDown;
  };

  const getTrendColor = (change: number, inverse = false) => {
    const isPositive = inverse ? change < 0 : change > 0;
    return isPositive ? 'text-green-600' : 'text-red-600';
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Page Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.pageViews.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {(() => {
                const TrendIcon = getTrendIcon(data.trends.pageViews.change);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 mr-1 ${getTrendColor(data.trends.pageViews.change)}`} />
                    <span className={getTrendColor(data.trends.pageViews.change)}>
                      {Math.abs(data.trends.pageViews.change)}%
                    </span>
                    <span className="ml-1">from last period</span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.uniqueVisitors.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {(() => {
                const TrendIcon = getTrendIcon(data.trends.uniqueVisitors.change);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 mr-1 ${getTrendColor(data.trends.uniqueVisitors.change)}`} />
                    <span className={getTrendColor(data.trends.uniqueVisitors.change)}>
                      {Math.abs(data.trends.uniqueVisitors.change)}%
                    </span>
                    <span className="ml-1">from last period</span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Time on Page</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatTime(data.averageTimeOnPage)}</div>
            <p className="text-xs text-muted-foreground">
              Good engagement time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.bounceRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {(() => {
                const TrendIcon = getTrendIcon(data.trends.bounceRate.change);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 mr-1 ${getTrendColor(data.trends.bounceRate.change, true)}`} />
                    <span className={getTrendColor(data.trends.bounceRate.change, true)}>
                      {Math.abs(data.trends.bounceRate.change)}%
                    </span>
                    <span className="ml-1">from last period</span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Metrics
          </CardTitle>
          <CardDescription>
            Technical performance and optimization scores
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Load Time</span>
                <span className="text-sm text-muted-foreground">{data.performanceMetrics.loadTime}s</span>
              </div>
              <Progress value={Math.max(0, 100 - (data.performanceMetrics.loadTime * 20))} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">SEO Score</span>
                <Badge className={getScoreBadgeColor(data.performanceMetrics.seoScore)}>
                  {data.performanceMetrics.seoScore}/100
                </Badge>
              </div>
              <Progress value={data.performanceMetrics.seoScore} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Accessibility</span>
                <Badge className={getScoreBadgeColor(data.performanceMetrics.accessibilityScore)}>
                  {data.performanceMetrics.accessibilityScore}/100
                </Badge>
              </div>
              <Progress value={data.performanceMetrics.accessibilityScore} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Performance</span>
                <Badge className={getScoreBadgeColor(data.performanceMetrics.performanceScore)}>
                  {data.performanceMetrics.performanceScore}/100
                </Badge>
              </div>
              <Progress value={data.performanceMetrics.performanceScore} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Traffic Sources and Device Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Top Referrers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topReferrers.map((referrer, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{referrer.source}</div>
                    <div className="text-xs text-muted-foreground">
                      {referrer.visits.toLocaleString()} visits
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{referrer.percentage}%</div>
                    <Progress value={referrer.percentage} className="w-16 h-1 mt-1" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Device Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.deviceBreakdown.map((device, index) => {
                const DeviceIcon = device.icon;
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DeviceIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium text-sm">{device.device}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{device.percentage}%</div>
                      <Progress value={device.percentage} className="w-16 h-1 mt-1" />
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
