'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronRight, 
  ChevronDown, 
  FileText, 
  Globe, 
  Plus,
  Edit,
  Eye,
  MoreHorizontal,
  GripVertical
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { PageWithRelations, ContentStatus } from '@/types';

interface PageTreeNode extends PageWithRelations {
  children?: PageTreeNode[];
  level: number;
}

interface PageTreeProps {
  pages: PageWithRelations[];
  onEdit: (pageId: string) => void;
  onPreview: (pageSlug: string) => void;
  onDelete: (pageId: string) => void;
  onAddChild: (parentId: string) => void;
  onReorder: (pageId: string, newParentId?: string, newOrder?: number) => void;
}

export function PageTree({ 
  pages, 
  onEdit, 
  onPreview, 
  onDelete, 
  onAddChild, 
  onReorder 
}: PageTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // Build hierarchical tree structure
  const buildTree = (pages: PageWithRelations[]): PageTreeNode[] => {
    const pageMap = new Map<string, PageTreeNode>();
    const rootPages: PageTreeNode[] = [];

    // First pass: create all nodes
    pages.forEach(page => {
      pageMap.set(page.id, { ...page, children: [], level: 0 });
    });

    // Second pass: build hierarchy
    pages.forEach(page => {
      const node = pageMap.get(page.id)!;
      
      // For now, we'll treat all pages as root level since we don't have parent_id in the schema
      // In a real implementation, you'd check for parent_id
      rootPages.push(node);
    });

    // Sort by title for now (in real implementation, use sort_order)
    rootPages.sort((a, b) => a.title.localeCompare(b.title));

    return rootPages;
  };

  const treeData = buildTree(pages);

  const toggleExpanded = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getStatusBadge = (status: ContentStatus) => {
    const variants = {
      draft: 'secondary',
      pending_review: 'outline',
      published: 'default',
      archived: 'destructive',
      rejected: 'destructive',
    } as const;

    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-red-100 text-red-800',
      rejected: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colors[status] || colors.draft}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const renderNode = (node: PageTreeNode) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id);

    return (
      <div key={node.id} className="space-y-1">
        <div 
          className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 group"
          style={{ paddingLeft: `${node.level * 20 + 8}px` }}
        >
          {/* Drag Handle */}
          <GripVertical className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 cursor-grab" />

          {/* Expand/Collapse Button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => toggleExpanded(node.id)}
            disabled={!hasChildren}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )
            ) : (
              <div className="h-3 w-3" />
            )}
          </Button>

          {/* Page Icon */}
          <FileText className="h-4 w-4 text-muted-foreground" />

          {/* Page Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium truncate">{node.title}</span>
              {getStatusBadge(node.status)}
            </div>
            <div className="text-xs text-muted-foreground flex items-center gap-1">
              <Globe className="h-3 w-3" />
              /{node.slug}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => onPreview(node.slug)}
            >
              <Eye className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => onEdit(node.id)}
            >
              <Edit className="h-3 w-3" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onAddChild(node.id)}>
                  <Plus className="h-3 w-3 mr-2" />
                  Add Child Page
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit(node.id)}>
                  <Edit className="h-3 w-3 mr-2" />
                  Edit Page
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onPreview(node.slug)}>
                  <Eye className="h-3 w-3 mr-2" />
                  Preview Page
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => onDelete(node.id)}
                  className="text-destructive"
                >
                  <FileText className="h-3 w-3 mr-2" />
                  Delete Page
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Render Children */}
        {hasChildren && isExpanded && (
          <div className="space-y-1">
            {node.children!.map(child => renderNode({ ...child, level: node.level + 1 }))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Page Structure
            </CardTitle>
            <CardDescription>
              Hierarchical view of your website pages with drag-and-drop reordering
            </CardDescription>
          </div>
          <Button size="sm" onClick={() => onAddChild('')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Root Page
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {treeData.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No pages found</h3>
            <p className="text-muted-foreground mb-4">
              Create your first page to get started.
            </p>
            <Button onClick={() => onAddChild('')}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Page
            </Button>
          </div>
        ) : (
          <div className="space-y-1">
            {treeData.map(node => renderNode(node))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
