'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PageEditorForm } from '@/components/pages';
import { usePageEditor } from '@/hooks/use-page-editor';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

export default function NewPagePage() {
  const router = useRouter();
  const {
    formData,
    authors,
    loading,
    errors,
    isDirty,
    lastSaved,
    updateFormData,
    savePage,
    validateForm,
  } = usePageEditor();

  // Load template data if available
  useEffect(() => {
    const templateData = sessionStorage.getItem('pageTemplate');
    if (templateData) {
      try {
        const template = JSON.parse(templateData);
        updateFormData({
          title: template.title || '',
          content: template.content || {},
          meta_title: template.meta_title || '',
          meta_description: template.meta_description || '',
        });
        // Clear the template data after loading
        sessionStorage.removeItem('pageTemplate');
      } catch (error) {
        console.error('Failed to load template data:', error);
      }
    }
  }, [updateFormData]);

  const handleSave = async () => {
    const result = await savePage();
    if (result.success && result.data) {
      // Redirect to edit page after successful creation
      router.push(`/web-pages/${result.data.id}/edit`);
    }
    return result;
  };

  const hasErrors = Object.values(errors).some((error) => error);

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Back Button */}
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Pages
        </Button>
      </div>

      {/* Loading State */}
      {loading.authors && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>Loading editor data...</AlertDescription>
        </Alert>
      )}

      {/* Error State */}
      {hasErrors && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Failed to load editor data:</p>
              <ul className="list-disc list-inside text-sm">
                {Object.entries(errors).map(
                  ([key, error]) =>
                    error && (
                      <li key={key}>
                        {key}:{' '}
                        {typeof error === 'string'
                          ? error
                          : JSON.stringify(error)}
                      </li>
                    )
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Page Editor Form */}
      <PageEditorForm
        formData={formData}
        authors={authors}
        onFormDataChange={updateFormData}
        onSave={handleSave}
        isLoading={loading.saving}
        isSaving={loading.saving}
        isDirty={isDirty}
        lastSaved={lastSaved}
        errors={errors.validation}
        isEditing={false}
      />
    </div>
  );
}
