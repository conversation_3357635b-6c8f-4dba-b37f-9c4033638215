'use client';

import { useParams, useRouter } from 'next/navigation';
import { PageEditorForm } from '@/components/pages';
import { usePageEditor } from '@/hooks/use-page-editor';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

export default function EditPagePage() {
  const params = useParams();
  const router = useRouter();
  const pageSlug = params.slug as string;

  const {
    formData,
    originalPage,
    authors,
    loading,
    errors,
    isDirty,
    lastSaved,
    updateFormData,
    savePage,
  } = usePageEditor(pageSlug);

  const handleSave = async () => {
    const result = await savePage();
    return result;
  };

  const hasErrors = Object.values(errors).some((error) => error);

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Back Button */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/web-pages')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Pages
        </Button>
      </div>

      {/* Loading State */}
      {(loading.page || loading.authors) && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>Loading page data...</AlertDescription>
        </Alert>
      )}

      {/* Error State */}
      {hasErrors && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Failed to load page data:</p>
              <ul className="list-disc list-inside text-sm">
                {Object.entries(errors).map(
                  ([key, error]) =>
                    error && (
                      <li key={key}>
                        {key}:{' '}
                        {typeof error === 'string'
                          ? error
                          : JSON.stringify(error)}
                      </li>
                    )
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Page Editor Form */}
      {originalPage && !loading.page && (
        <PageEditorForm
          formData={formData}
          authors={authors}
          onFormDataChange={updateFormData}
          onSave={handleSave}
          isLoading={loading.saving}
          isSaving={loading.saving}
          isDirty={isDirty}
          lastSaved={lastSaved}
          errors={errors.validation}
          isEditing={true}
        />
      )}
    </div>
  );
}
