'use client';

import * as React from 'react';
import {
  LayoutDashboard,
  FileText,
  Users,
  Shield,
  type LucideIcon,
} from 'lucide-react';

import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { FSNCBranding } from '@/components/fsnc-branding';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar';

// Navigation configuration for FSNC Dashboard
export interface NavItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: {
    title: string;
    url: string;
  }[];
}

const navigationData: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: LayoutDashboard,
    isActive: true,
  },
  {
    title: 'Content',
    url: '#',
    icon: FileText,
    items: [
      {
        title: 'Posts',
        url: '/posts',
      },
      {
        title: 'Pages',
        url: '/web-pages',
      },
      {
        title: 'Categories',
        url: '/categories',
      },
      {
        title: 'Tags',
        url: '/tags',
      },
      {
        title: 'Biographies',
        url: '/biographies',
      },
    ],
  },
  {
    title: 'Users',
    url: '#',
    icon: Users,
    items: [
      {
        title: 'All Users',
        url: '/users',
      },
      {
        title: 'My Profile',
        url: '/profile',
      },
    ],
  },
  {
    title: 'System',
    url: '#',
    icon: Shield,
    items: [
      {
        title: 'Audit Log',
        url: '/audit-log',
      },
      {
        title: 'Settings',
        url: '/settings',
      },
    ],
  },
];
// Sample user data - this should be replaced with actual user data from auth
const sampleUser = {
  name: 'Admin User',
  email: '<EMAIL>',
  avatar: '/avatars/default.jpg',
};

export interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: {
    name: string;
    email: string;
    avatar: string;
  };
}

export function AppSidebar({ user, ...props }: AppSidebarProps) {
  const currentUser = user || sampleUser;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <FSNCBranding />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigationData} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={currentUser} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
