'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  getPost,
  createPost,
  updatePost,
  getCategories,
  getAuthors,
  getTags,
  createTag,
} from '@/lib/database-helpers';
import { generateSlug, validateSlug } from '@/lib/slug-utils';
import type {
  PostWithRelations,
  InsertPost,
  UpdatePost,
  Category,
  Profile,
  Tag,
  ContentStatus,
} from '@/types';

export interface PostFormData {
  title: string;
  slug: string;
  content: any;
  excerpt: string;
  featured_image_url: string;
  category_id: string;
  tags: Tag[];
  meta_title: string;
  meta_description: string;
  status: ContentStatus;
  published_at: string;
  author_id: string;
}

interface PostEditorState {
  formData: PostFormData;
  originalPost?: PostWithRelations;
  categories: Category[];
  authors: Profile[];
  tags: Tag[];
  loading: {
    post: boolean;
    categories: boolean;
    authors: boolean;
    tags: boolean;
    saving: boolean;
  };
  errors: {
    post?: string;
    categories?: string;
    authors?: string;
    tags?: string;
    saving?: string;
    validation?: Record<string, string>;
  };
  isDirty: boolean;
  lastSaved?: Date;
  autoSaveEnabled: boolean;
}

export function usePostEditor(postId?: string) {
  const [state, setState] = useState<PostEditorState>({
    formData: {
      title: '',
      slug: '',
      content: null,
      excerpt: '',
      featured_image_url: '',
      category_id: '',
      tags: [],
      meta_title: '',
      meta_description: '',
      status: 'draft',
      published_at: '',
      author_id: '',
    },
    categories: [],
    authors: [],
    tags: [],
    loading: {
      post: !!postId,
      categories: true,
      authors: true,
      tags: true,
      saving: false,
    },
    errors: {},
    isDirty: false,
    autoSaveEnabled: true,
  });

  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch post data if editing
  const fetchPost = useCallback(async () => {
    if (!postId) return;

    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, post: true },
      errors: { ...prev.errors, post: undefined },
    }));

    try {
      const result = await getPost(postId);

      if (result.data) {
        const post = result.data;
        setState((prev) => ({
          ...prev,
          originalPost: post,
          formData: {
            title: post.title,
            slug: post.slug,
            content: post.content,
            excerpt: post.excerpt || '',
            featured_image_url: post.featured_image_url || '',
            category_id: post.category_id || '',
            tags: post.tags || [],
            meta_title: post.meta_title || '',
            meta_description: post.meta_description || '',
            status: post.status,
            published_at: post.published_at
              ? post.published_at.toISOString()
              : '',
            author_id: post.author_id || '', // Ensure author_id is never undefined
          },
          loading: { ...prev.loading, post: false },
        }));
      } else {
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, post: false },
          errors: {
            ...prev.errors,
            post: result.error || 'Failed to fetch post',
          },
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, post: false },
        errors: {
          ...prev.errors,
          post: error instanceof Error ? error.message : 'Failed to fetch post',
        },
      }));
    }
  }, [postId]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, categories: true },
      errors: { ...prev.errors, categories: undefined },
    }));

    try {
      const result = await getCategories();

      if (result.data) {
        setState((prev) => ({
          ...prev,
          categories: result.data!,
          loading: { ...prev.loading, categories: false },
        }));
      } else {
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, categories: false },
          errors: {
            ...prev.errors,
            categories: result.error || 'Failed to fetch categories',
          },
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, categories: false },
        errors: {
          ...prev.errors,
          categories:
            error instanceof Error
              ? error.message
              : 'Failed to fetch categories',
        },
      }));
    }
  }, []);

  // Fetch authors
  const fetchAuthors = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, authors: true },
      errors: { ...prev.errors, authors: undefined },
    }));

    try {
      const result = await getAuthors();

      if (result.data) {
        setState((prev) => ({
          ...prev,
          authors: result.data!,
          loading: { ...prev.loading, authors: false },
        }));
      } else {
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, authors: false },
          errors: {
            ...prev.errors,
            authors: result.error || 'Failed to fetch authors',
          },
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, authors: false },
        errors: {
          ...prev.errors,
          authors:
            error instanceof Error ? error.message : 'Failed to fetch authors',
        },
      }));
    }
  }, []);

  // Fetch tags
  const fetchTags = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, tags: true },
      errors: { ...prev.errors, tags: undefined },
    }));

    try {
      const result = await getTags();

      if (result.data) {
        setState((prev) => ({
          ...prev,
          tags: result.data!,
          loading: { ...prev.loading, tags: false },
        }));
      } else {
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, tags: false },
          errors: {
            ...prev.errors,
            tags: result.error || 'Failed to fetch tags',
          },
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, tags: false },
        errors: {
          ...prev.errors,
          tags: error instanceof Error ? error.message : 'Failed to fetch tags',
        },
      }));
    }
  }, []);

  // Update form data
  const updateFormData = useCallback(
    (updates: Partial<PostFormData>) => {
      setState((prev) => ({
        ...prev,
        formData: { ...prev.formData, ...updates },
        isDirty: true,
      }));

      // Auto-generate slug from title if title is updated and slug is empty or auto-generated
      if (
        updates.title &&
        (!state.formData.slug ||
          state.formData.slug === generateSlug(state.formData.title))
      ) {
        const newSlug = generateSlug(updates.title);
        setState((prev) => ({
          ...prev,
          formData: { ...prev.formData, slug: newSlug },
        }));
      }

      // Trigger auto-save
      if (state.autoSaveEnabled && postId) {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
        autoSaveTimeoutRef.current = setTimeout(() => {
          autoSave();
        }, 3000); // Auto-save after 3 seconds of inactivity
      }
    },
    [state.formData, state.autoSaveEnabled, postId]
  );

  // Validate form data
  const validateForm = useCallback((): {
    isValid: boolean;
    errors: Record<string, string>;
  } => {
    const errors: Record<string, string> = {};

    if (!state.formData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!state.formData.slug.trim()) {
      errors.slug = 'Slug is required';
    } else {
      const slugValidation = validateSlug(state.formData.slug);
      if (!slugValidation.isValid) {
        errors.slug = slugValidation.errors[0];
      }
    }

    if (!state.formData.author_id) {
      errors.author_id = 'Author is required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }, [state.formData]);

  // Auto-save function
  const autoSave = useCallback(async () => {
    if (!postId || !state.isDirty) return;

    try {
      const validation = validateForm();
      if (!validation.isValid) return; // Don't auto-save invalid data

      const updateData: Omit<UpdatePost, 'id'> = {
        title: state.formData.title,
        slug: state.formData.slug,
        content: state.formData.content,
        excerpt: state.formData.excerpt || undefined,
        featured_image_url: state.formData.featured_image_url || undefined,
        category_id: state.formData.category_id || undefined,
        meta_title: state.formData.meta_title || undefined,
        meta_description: state.formData.meta_description || undefined,
        status: state.formData.status,
        published_at: state.formData.published_at
          ? new Date(state.formData.published_at)
          : undefined,
      };

      await updatePost(postId, updateData);

      setState((prev) => ({
        ...prev,
        isDirty: false,
        lastSaved: new Date(),
      }));
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [postId, state.formData, state.isDirty, validateForm]);

  // Save post
  const savePost = useCallback(async (): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, saving: true },
      errors: { ...prev.errors, saving: undefined, validation: undefined },
    }));

    try {
      const validation = validateForm();
      if (!validation.isValid) {
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, saving: false },
          errors: { ...prev.errors, validation: validation.errors },
        }));
        return { success: false, error: 'Please fix validation errors' };
      }

      if (postId) {
        // Update existing post
        const updateData: Omit<UpdatePost, 'id'> = {
          title: state.formData.title,
          slug: state.formData.slug,
          content: state.formData.content,
          excerpt: state.formData.excerpt || undefined,
          featured_image_url: state.formData.featured_image_url || undefined,
          category_id: state.formData.category_id || undefined,
          meta_title: state.formData.meta_title || undefined,
          meta_description: state.formData.meta_description || undefined,
          status: state.formData.status,
          published_at: state.formData.published_at
            ? new Date(state.formData.published_at)
            : undefined,
        };

        const result = await updatePost(postId, updateData);

        if (result.data) {
          setState((prev) => ({
            ...prev,
            loading: { ...prev.loading, saving: false },
            isDirty: false,
            lastSaved: new Date(),
          }));
          return { success: true, data: result.data };
        } else {
          setState((prev) => ({
            ...prev,
            loading: { ...prev.loading, saving: false },
            errors: {
              ...prev.errors,
              saving: result.error || 'Failed to update post',
            },
          }));
          return { success: false, error: result.error };
        }
      } else {
        // Create new post
        const insertData: InsertPost = {
          title: state.formData.title,
          slug: state.formData.slug,
          content: state.formData.content,
          excerpt: state.formData.excerpt || undefined,
          featured_image_url: state.formData.featured_image_url || undefined,
          author_id: state.formData.author_id,
          category_id: state.formData.category_id || undefined,
          meta_title: state.formData.meta_title || undefined,
          meta_description: state.formData.meta_description || undefined,
          status: state.formData.status,
          published_at: state.formData.published_at
            ? new Date(state.formData.published_at)
            : undefined,
        };

        const result = await createPost(insertData);

        if (result.data) {
          setState((prev) => ({
            ...prev,
            loading: { ...prev.loading, saving: false },
            isDirty: false,
            lastSaved: new Date(),
          }));
          return { success: true, data: result.data };
        } else {
          setState((prev) => ({
            ...prev,
            loading: { ...prev.loading, saving: false },
            errors: {
              ...prev.errors,
              saving: result.error || 'Failed to create post',
            },
          }));
          return { success: false, error: result.error };
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to save post';
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, saving: false },
        errors: { ...prev.errors, saving: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [postId, state.formData, validateForm]);

  // Create new tag
  const handleCreateTag = useCallback(
    async (name: string): Promise<Tag | null> => {
      try {
        const result = await createTag({ name, slug: generateSlug(name) });
        if (result.data) {
          // Add to available tags
          setState((prev) => ({
            ...prev,
            tags: [...prev.tags, result.data!],
          }));
          return result.data;
        }
        return null;
      } catch (error) {
        console.error('Failed to create tag:', error);
        return null;
      }
    },
    []
  );

  // Toggle auto-save
  const toggleAutoSave = useCallback(() => {
    setState((prev) => ({
      ...prev,
      autoSaveEnabled: !prev.autoSaveEnabled,
    }));
  }, []);

  // Initial data fetch
  useEffect(() => {
    if (postId) {
      fetchPost();
    }
    fetchCategories();
    fetchAuthors();
    fetchTags();
  }, [postId, fetchPost, fetchCategories, fetchAuthors, fetchTags]);

  // Cleanup auto-save timeout
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
        autoSaveTimeoutRef.current = null;
      }
    };
  }, []);

  return {
    // State
    formData: state.formData,
    originalPost: state.originalPost,
    categories: state.categories,
    authors: state.authors,
    tags: state.tags,
    loading: state.loading,
    errors: state.errors,
    isDirty: state.isDirty,
    lastSaved: state.lastSaved,
    autoSaveEnabled: state.autoSaveEnabled,

    // Actions
    updateFormData,
    savePost,
    validateForm,
    createTag: handleCreateTag,
    toggleAutoSave,

    // Computed
    isLoading: Object.values(state.loading).some(Boolean),
    hasErrors: Object.values(state.errors).some(Boolean),
    isEditing: !!postId,
  };
}

