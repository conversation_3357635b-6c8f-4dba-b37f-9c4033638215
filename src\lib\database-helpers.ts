/**
 * Database helper functions with proper TypeScript typing
 * Examples of how to use the generated database types
 */

import { createClient } from './client';
import type {
  Post,
  PostWithRelations,
  InsertPost,
  UpdatePost,
  Page,
  PageWithRelations,
  InsertPage,
  UpdatePage,
  Profile,
  Category,
  Tag,
  InsertTag,
  ContentStatus,
  UserRole,
  PaginatedResponse,
  QueryParams,
} from '@/types';

// =============================================================================
// POST OPERATIONS
// =============================================================================

/**
 * Get all posts with optional filtering and pagination
 */
export async function getPosts(
  params: QueryParams = {}
): Promise<PaginatedResponse<PostWithRelations>> {
  const supabase = createClient();
  const {
    page = 1,
    limit = 10,
    search,
    status,
    category,
    author,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = params;

  let query = supabase
    .from('posts')
    .select(
      `
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email)
    `
    )
    .is('deleted_at', null);

  // Apply filters
  if (search) {
    query = query.or(`title.ilike.%${search}%, excerpt.ilike.%${search}%`);
  }
  if (status) {
    query = query.eq('status', status as ContentStatus);
  }
  if (category) {
    query = query.eq('category_id', category);
  }
  if (author) {
    query = query.eq('author_id', author);
  }

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    success: true,
    data: data as PostWithRelations[],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Get a single post by ID or slug
 */
export async function getPost(
  idOrSlug: string
): Promise<{ data?: PostWithRelations; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .select(
      `
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      tags:post_tags!inner(tag:tags!inner(id, name, slug)),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email)
    `
    )
    .or(`id.eq.${idOrSlug}, slug.eq.${idOrSlug}`)
    .is('deleted_at', null)
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data: data as PostWithRelations };
}

/**
 * Create a new post
 */
export async function createPost(
  postData: InsertPost
): Promise<{ data?: Post; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .insert(postData)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Update an existing post
 */
export async function updatePost(
  id: string,
  updates: Omit<UpdatePost, 'id'>
): Promise<{ data?: Post; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Delete a post (soft delete)
 */
export async function deletePost(
  id: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  const { error } = await supabase
    .from('posts')
    .update({ deleted_at: new Date().toISOString() })
    .eq('id', id);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

/**
 * Bulk update posts
 */
export async function bulkUpdatePosts(
  ids: string[],
  updates: Partial<Pick<Post, 'status' | 'category_id'>>
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  const { error } = await supabase
    .from('posts')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .in('id', ids);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

/**
 * Bulk delete posts (soft delete)
 */
export async function bulkDeletePosts(
  ids: string[]
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  const { error } = await supabase
    .from('posts')
    .update({ deleted_at: new Date().toISOString() })
    .in('id', ids);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

/**
 * Duplicate a post
 */
export async function duplicatePost(
  id: string
): Promise<{ data?: Post; error?: string }> {
  const supabase = createClient();

  // First, get the original post
  const { data: originalPost, error: fetchError } = await supabase
    .from('posts')
    .select('*')
    .eq('id', id)
    .single();

  if (fetchError) {
    return { error: fetchError.message };
  }

  // Create a new post with duplicated data
  const duplicatedPost: InsertPost = {
    title: `${originalPost.title} (Copy)`,
    slug: `${originalPost.slug}-copy-${Date.now()}`,
    content: originalPost.content,
    excerpt: originalPost.excerpt,
    featured_image_url: originalPost.featured_image_url,
    author_id: originalPost.author_id,
    status: 'draft', // Always create duplicates as drafts
    category_id: originalPost.category_id,
    meta_title: originalPost.meta_title,
    meta_description: originalPost.meta_description,
  };

  const { data, error } = await supabase
    .from('posts')
    .insert(duplicatedPost)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// PROFILE OPERATIONS
// =============================================================================

/**
 * Get user profile by ID
 */
export async function getProfile(
  id: string
): Promise<{ data?: Profile; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', id)
    .is('deleted_at', null)
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Update user profile
 */
export async function updateProfile(
  id: string,
  updates: Partial<Pick<Profile, 'full_name' | 'avatar_url'>>
): Promise<{ data?: Profile; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('profiles')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// CATEGORY OPERATIONS
// =============================================================================

/**
 * Get all categories
 */
export async function getCategories(): Promise<{
  data?: Category[];
  error?: string;
}> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .is('deleted_at', null)
    .order('name');

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Get all authors (profiles who have written posts)
 */
export async function getAuthors(): Promise<{
  data?: Profile[];
  error?: string;
}> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('profiles')
    .select('id, full_name, email, avatar_url, role')
    .is('deleted_at', null)
    .order('full_name');

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Get posts with tags for advanced filtering
 */
export async function getPostsWithTags(
  params: QueryParams = {}
): Promise<PaginatedResponse<PostWithRelations>> {
  const supabase = createClient();
  const {
    page = 1,
    limit = 10,
    search,
    status,
    category,
    author,
    tag,
    dateFrom,
    dateTo,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = params;

  let query = supabase
    .from('posts')
    .select(
      `
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email),
      tags:post_tags!inner(tag:tags!inner(id, name, slug))
    `
    )
    .is('deleted_at', null);

  // Apply filters
  if (search) {
    query = query.or(
      `title.ilike.%${search}%, excerpt.ilike.%${search}%, content::text.ilike.%${search}%`
    );
  }
  if (status) {
    query = query.eq('status', status as ContentStatus);
  }
  if (category) {
    query = query.eq('category_id', category);
  }
  if (author) {
    query = query.eq('author_id', author);
  }
  if (dateFrom) {
    query = query.gte('created_at', dateFrom);
  }
  if (dateTo) {
    query = query.lte('created_at', dateTo);
  }

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    success: true,
    data: data as PostWithRelations[],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Search posts by tag
 */
export async function searchPostsByTag(
  tagId: string,
  params: QueryParams = {}
): Promise<PaginatedResponse<PostWithRelations>> {
  const supabase = createClient();
  const {
    page = 1,
    limit = 10,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = params;

  let query = supabase
    .from('posts')
    .select(
      `
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email),
      tags:post_tags!inner(tag:tags!inner(id, name, slug))
    `
    )
    .is('deleted_at', null);

  // Filter by tag through the post_tags junction table
  const { data: postTags, error: tagError } = await supabase
    .from('post_tags')
    .select('post_id')
    .eq('tag_id', tagId);

  if (tagError) {
    return {
      success: false,
      error: tagError.message,
    };
  }

  const postIds = postTags.map((pt) => pt.post_id);
  if (postIds.length === 0) {
    return {
      success: true,
      data: [],
      pagination: {
        page,
        limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  query = query.in('id', postIds);

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    success: true,
    data: data as PostWithRelations[],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// =============================================================================
// PAGE OPERATIONS
// =============================================================================

/**
 * Get all pages with optional filtering and pagination
 */
export async function getPages(
  params: QueryParams = {}
): Promise<PaginatedResponse<PageWithRelations>> {
  const supabase = createClient();
  const {
    page = 1,
    limit = 10,
    search,
    status,
    author,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = params;

  let query = supabase
    .from('pages')
    .select(
      `
      *,
      author:profiles!pages_author_id_fkey(id, full_name, email, avatar_url),
      last_edited_by:profiles!pages_last_edited_by_id_fkey(id, full_name, email)
    `
    )
    .is('deleted_at', null);

  // Apply filters
  if (search) {
    query = query.or(
      `title.ilike.%${search}%, meta_description.ilike.%${search}%`
    );
  }
  if (status) {
    query = query.eq('status', status as ContentStatus);
  }
  if (author) {
    query = query.eq('author_id', author);
  }

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    success: true,
    data: data as PageWithRelations[],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Get a single page by ID or slug
 */
export async function getPage(
  idOrSlug: string
): Promise<{ data?: PageWithRelations; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('pages')
    .select(
      `
      *,
      author:profiles!pages_author_id_fkey(id, full_name, email, avatar_url),
      last_edited_by:profiles!pages_last_edited_by_id_fkey(id, full_name, email)
    `
    )
    .or(`id.eq.${idOrSlug}, slug.eq.${idOrSlug}`)
    .is('deleted_at', null)
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data: data as PageWithRelations };
}

/**
 * Create a new page
 */
export async function createPage(
  pageData: InsertPage
): Promise<{ data?: Page; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('pages')
    .insert(pageData)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Update an existing page
 */
export async function updatePage(
  id: string,
  updates: Omit<UpdatePage, 'id'>
): Promise<{ data?: Page; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('pages')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Delete a page (soft delete)
 */
export async function deletePage(
  id: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  const { error } = await supabase
    .from('pages')
    .update({ deleted_at: new Date().toISOString() })
    .eq('id', id);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

/**
 * Duplicate a page
 */
export async function duplicatePage(
  id: string,
  newTitle?: string
): Promise<{ data?: Page; error?: string }> {
  const supabase = createClient();

  // First, get the original page
  const { data: originalPage, error: fetchError } = await supabase
    .from('pages')
    .select('*')
    .eq('id', id)
    .single();

  if (fetchError || !originalPage) {
    return { error: fetchError?.message || 'Page not found' };
  }

  // Create a new page with duplicated data
  const duplicatedData: InsertPage = {
    title: newTitle || `${originalPage.title} (Copy)`,
    slug: `${originalPage.slug}-copy-${Date.now()}`,
    content: originalPage.content,
    author_id: originalPage.author_id,
    status: 'draft' as ContentStatus,
    meta_title: originalPage.meta_title,
    meta_description: originalPage.meta_description,
  };

  return createPage(duplicatedData);
}

// =============================================================================
// TAG OPERATIONS
// =============================================================================

/**
 * Get all tags
 */
export async function getTags(): Promise<{ data?: Tag[]; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .is('deleted_at', null)
    .order('name');

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Create a new tag
 */
export async function createTag(
  tagData: InsertTag
): Promise<{ data?: Tag; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('tags')
    .insert(tagData)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}

/**
 * Check if a slug is unique
 */
export async function isSlugUnique(
  slug: string,
  table: 'posts' | 'pages' | 'categories' | 'tags',
  excludeId?: string
): Promise<boolean> {
  const supabase = createClient();

  let query = supabase
    .from(table)
    .select('id')
    .eq('slug', slug)
    .is('deleted_at', null);

  if (excludeId) {
    query = query.neq('id', excludeId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error checking slug uniqueness:', error);
    return false;
  }

  return data.length === 0;
}

/**
 * Get content status options for forms
 */
export function getContentStatusOptions(): Array<{
  value: ContentStatus;
  label: string;
}> {
  return [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_review', label: 'Pending Review' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'published', label: 'Published' },
    { value: 'archived', label: 'Archived' },
  ];
}

/**
 * Get user role options for forms
 */
export function getUserRoleOptions(): Array<{
  value: UserRole;
  label: string;
}> {
  return [
    { value: 'admin', label: 'Administrator' },
    { value: 'publisher', label: 'Publisher' },
    { value: 'editor', label: 'Editor' },
    { value: 'member', label: 'Member' },
  ];
}
