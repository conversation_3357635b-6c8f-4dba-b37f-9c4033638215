'use client';

import { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { X, Plus, Tag as TagIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Tag } from '@/types';

interface TagInputProps {
  selectedTags: Tag[];
  availableTags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  onCreateTag?: (name: string) => Promise<Tag | null>;
  placeholder?: string;
  className?: string;
  maxTags?: number;
}

export function TagInput({
  selectedTags,
  availableTags,
  onTagsChange,
  onCreateTag,
  placeholder = 'Add tags...',
  className,
  maxTags = 10,
}: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
    !selectedTags.some(selected => selected.id === tag.id)
  );

  const handleSelectTag = (tag: Tag) => {
    if (selectedTags.length >= maxTags) return;
    
    onTagsChange([...selectedTags, tag]);
    setInputValue('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const handleRemoveTag = (tagId: string) => {
    onTagsChange(selectedTags.filter(tag => tag.id !== tagId));
  };

  const handleCreateTag = async () => {
    if (!inputValue.trim() || !onCreateTag) return;
    
    setIsCreating(true);
    try {
      const newTag = await onCreateTag(inputValue.trim());
      if (newTag) {
        handleSelectTag(newTag);
      }
    } catch (error) {
      console.error('Failed to create tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      
      // Check if there's an exact match
      const exactMatch = filteredTags.find(
        tag => tag.name.toLowerCase() === inputValue.toLowerCase()
      );
      
      if (exactMatch) {
        handleSelectTag(exactMatch);
      } else if (onCreateTag) {
        handleCreateTag();
      }
    } else if (e.key === 'Backspace' && !inputValue && selectedTags.length > 0) {
      // Remove last tag when backspace is pressed on empty input
      handleRemoveTag(selectedTags[selectedTags.length - 1].id);
    }
  };

  const canCreateTag = inputValue.trim() && 
    !filteredTags.some(tag => tag.name.toLowerCase() === inputValue.toLowerCase()) &&
    onCreateTag;

  return (
    <div className={cn('space-y-2', className)}>
      {/* Selected Tags */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map((tag) => (
            <Badge key={tag.id} variant="secondary" className="flex items-center gap-1">
              <TagIcon className="h-3 w-3" />
              {tag.name}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => handleRemoveTag(tag.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Tag Input */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsOpen(true)}
              placeholder={selectedTags.length >= maxTags ? 'Maximum tags reached' : placeholder}
              disabled={selectedTags.length >= maxTags}
              className="pr-10"
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2">
              <TagIcon className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </PopoverTrigger>
        
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              value={inputValue}
              onValueChange={setInputValue}
              placeholder="Search tags..."
            />
            <CommandList>
              {filteredTags.length === 0 && !canCreateTag && (
                <CommandEmpty>No tags found.</CommandEmpty>
              )}
              
              {filteredTags.length > 0 && (
                <CommandGroup heading="Available Tags">
                  {filteredTags.map((tag) => (
                    <CommandItem
                      key={tag.id}
                      onSelect={() => handleSelectTag(tag)}
                      className="flex items-center gap-2"
                    >
                      <TagIcon className="h-4 w-4" />
                      <span>{tag.name}</span>
                      {tag.slug && (
                        <Badge variant="outline" className="text-xs">
                          {tag.slug}
                        </Badge>
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
              
              {canCreateTag && (
                <CommandGroup heading="Create New">
                  <CommandItem
                    onSelect={handleCreateTag}
                    disabled={isCreating}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    <span>
                      {isCreating ? 'Creating...' : `Create "${inputValue}"`}
                    </span>
                  </CommandItem>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Tag Count */}
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>
          {selectedTags.length} of {maxTags} tags selected
        </span>
        {selectedTags.length >= maxTags && (
          <span className="text-destructive">Maximum tags reached</span>
        )}
      </div>
    </div>
  );
}
