'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RichTextEditor } from '@/components/editor/rich-text-editor';
import { SEOAnalyzer } from './seo-analyzer';
import { PageVersionHistory } from './page-version-history';
import {
  FileText,
  Image,
  Settings,
  Eye,
  Save,
  Clock,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Globe,
  Search,
  Tag,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import type { ContentStatus, Profile } from '@/types';

interface PageFormData {
  title: string;
  slug: string;
  content: Record<string, any>;
  author_id?: string;
  status: ContentStatus;
  published_at?: Date;
  meta_title?: string;
  meta_description?: string;
  template_id?: string;
  parent_id?: string;
  sort_order?: number;
}

interface PageEditorFormProps {
  formData: PageFormData;
  authors: Profile[];
  onFormDataChange: (updates: Partial<PageFormData>) => void;
  onSave: () => Promise<{ success: boolean; data?: any; error?: string }>;
  isLoading?: boolean;
  isSaving?: boolean;
  isDirty?: boolean;
  lastSaved?: Date;
  errors?: Record<string, string>;
  isEditing?: boolean;
}

export function PageEditorForm({
  formData,
  authors,
  onFormDataChange,
  onSave,
  isLoading = false,
  isSaving = false,
  isDirty = false,
  lastSaved,
  errors = {},
  isEditing = false,
}: PageEditorFormProps) {
  const [activeTab, setActiveTab] = useState('content');

  const handleSlugChange = (slug: string) => {
    const cleanSlug = slug
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    onFormDataChange({ slug: cleanSlug });
  };

  const validateSlug = (slug: string) => {
    if (!slug) return { isValid: false, errors: ['URL slug is required'] };
    if (!/^[a-z0-9-]+$/.test(slug)) {
      return {
        isValid: false,
        errors: [
          'URL slug can only contain lowercase letters, numbers, and hyphens',
        ],
      };
    }
    return { isValid: true, errors: [] };
  };

  const slugValidation = validateSlug(formData.slug);

  const handleSave = async () => {
    const result = await onSave();
    return result;
  };

  const getStatusColor = (status: ContentStatus) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-red-100 text-red-800',
      rejected: 'bg-red-100 text-red-800',
    };
    return colors[status] || colors.draft;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isEditing ? 'Edit Page' : 'Create New Page'}
          </h1>
          <p className="text-muted-foreground">
            {isEditing
              ? 'Update your page content and settings'
              : 'Create a new static page for your website'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {lastSaved && (
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Saved {formatDistanceToNow(lastSaved)} ago
            </div>
          )}
          {isDirty && (
            <Badge
              variant="outline"
              className="text-orange-600 border-orange-600"
            >
              Unsaved changes
            </Badge>
          )}
          <Button
            onClick={handleSave}
            disabled={isSaving || isLoading}
            className="min-w-[100px]"
          >
            {isSaving ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEditing ? 'Update' : 'Create'} Page
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="content">
                <FileText className="h-4 w-4 mr-2" />
                Content
              </TabsTrigger>
              <TabsTrigger value="media">
                <Image className="h-4 w-4 mr-2" />
                Media
              </TabsTrigger>
              <TabsTrigger value="seo">
                <Settings className="h-4 w-4 mr-2" />
                SEO
              </TabsTrigger>
              <TabsTrigger value="analysis">
                <Search className="h-4 w-4 mr-2" />
                Analysis
              </TabsTrigger>
              <TabsTrigger value="history">
                <Clock className="h-4 w-4 mr-2" />
                History
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-6">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">Page Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => onFormDataChange({ title: e.target.value })}
                  placeholder="Enter page title..."
                  className={errors?.title ? 'border-destructive' : ''}
                />
                {errors?.title && (
                  <p className="text-sm text-destructive">{errors.title}</p>
                )}
              </div>

              {/* Slug */}
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">/</span>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => handleSlugChange(e.target.value)}
                    placeholder="url-friendly-slug"
                    className={
                      errors?.slug || !slugValidation.isValid
                        ? 'border-destructive'
                        : ''
                    }
                  />
                </div>
                {!slugValidation.isValid && (
                  <p className="text-sm text-destructive">
                    {slugValidation.errors[0]}
                  </p>
                )}
                {errors?.slug && (
                  <p className="text-sm text-destructive">{errors.slug}</p>
                )}
              </div>

              {/* Content Editor */}
              <div className="space-y-2">
                <Label>Page Content *</Label>
                <RichTextEditor
                  content={formData.content}
                  onChange={(content) => onFormDataChange({ content })}
                  placeholder="Start writing your page content..."
                />
              </div>
            </TabsContent>

            <TabsContent value="media" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Media Library</CardTitle>
                  <CardDescription>
                    Manage images and media files for this page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <Image className="h-12 w-12 mx-auto mb-4" />
                    <p>Media library integration coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              {/* Meta Title */}
              <div className="space-y-2">
                <Label htmlFor="meta_title">Meta Title</Label>
                <Input
                  id="meta_title"
                  value={formData.meta_title || ''}
                  onChange={(e) =>
                    onFormDataChange({ meta_title: e.target.value })
                  }
                  placeholder="SEO-optimized title for search engines"
                  maxLength={60}
                />
                <p className="text-xs text-muted-foreground">
                  {(formData.meta_title || '').length}/60 characters
                </p>
              </div>

              {/* Meta Description */}
              <div className="space-y-2">
                <Label htmlFor="meta_description">Meta Description</Label>
                <Textarea
                  id="meta_description"
                  value={formData.meta_description || ''}
                  onChange={(e) =>
                    onFormDataChange({ meta_description: e.target.value })
                  }
                  placeholder="Brief description for search engine results"
                  maxLength={160}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {(formData.meta_description || '').length}/160 characters
                </p>
              </div>

              {/* SEO Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    Search Engine Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                      {formData.meta_title || formData.title || 'Page Title'}
                    </div>
                    <div className="text-green-700 text-sm">
                      https://yoursite.com/{formData.slug || 'page-slug'}
                    </div>
                    <div className="text-gray-600 text-sm">
                      {formData.meta_description ||
                        'No meta description provided.'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analysis" className="space-y-6">
              <SEOAnalyzer
                title={formData.title}
                slug={formData.slug}
                content={formData.content}
                metaTitle={formData.meta_title}
                metaDescription={formData.meta_description}
              />
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              {isEditing ? (
                <PageVersionHistory
                  pageId="current-page-id" // TODO: Pass actual page ID
                  currentVersion={1}
                  onPreviewVersion={(versionId) => {
                    // TODO: Implement version preview
                    console.log('Preview version:', versionId);
                  }}
                  onRestoreVersion={(versionId) => {
                    // TODO: Implement version restore
                    console.log('Restore version:', versionId);
                  }}
                  onCompareVersions={(version1, version2) => {
                    // TODO: Implement version comparison
                    console.log('Compare versions:', version1, version2);
                  }}
                />
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Version History</CardTitle>
                    <CardDescription>
                      Version history will be available after you save this page
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      <Clock className="h-12 w-12 mx-auto mb-4" />
                      <p>Save your page to start tracking version history</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Page Preview</CardTitle>
                  <CardDescription>
                    Preview how your page will look to visitors
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <Eye className="h-12 w-12 mx-auto mb-4" />
                    <p>Live preview coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Publish Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: ContentStatus) =>
                    onFormDataChange({ status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-gray-400" />
                        Draft
                      </div>
                    </SelectItem>
                    <SelectItem value="pending_review">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-400" />
                        Pending Review
                      </div>
                    </SelectItem>
                    <SelectItem value="published">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-400" />
                        Published
                      </div>
                    </SelectItem>
                    <SelectItem value="archived">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-400" />
                        Archived
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Author */}
              <div className="space-y-2">
                <Label>Author</Label>
                <Select
                  value={formData.author_id || ''}
                  onValueChange={(value) =>
                    onFormDataChange({ author_id: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select author" />
                  </SelectTrigger>
                  <SelectContent>
                    {authors.map((author) => (
                      <SelectItem key={author.id} value={author.id}>
                        {author.full_name || author.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Publish Date */}
              <div className="space-y-2">
                <Label>Publish Date</Label>
                <Input
                  type="datetime-local"
                  value={
                    formData.published_at
                      ? formData.published_at.toISOString().slice(0, 16)
                      : ''
                  }
                  onChange={(e) =>
                    onFormDataChange({
                      published_at: e.target.value
                        ? new Date(e.target.value)
                        : undefined,
                    })
                  }
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to publish immediately
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Page Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Page Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Template */}
              <div className="space-y-2">
                <Label>Template</Label>
                <Select
                  value={formData.template_id || 'default'}
                  onValueChange={(value) =>
                    onFormDataChange({
                      template_id: value === 'default' ? undefined : value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Template</SelectItem>
                    <SelectItem value="landing">Landing Page</SelectItem>
                    <SelectItem value="about">About Page</SelectItem>
                    <SelectItem value="contact">Contact Page</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Parent Page */}
              <div className="space-y-2">
                <Label>Parent Page</Label>
                <Select
                  value={formData.parent_id || 'none'}
                  onValueChange={(value) =>
                    onFormDataChange({
                      parent_id: value === 'none' ? undefined : value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Parent (Top Level)</SelectItem>
                    {/* TODO: Add actual parent pages from database */}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Order */}
              <div className="space-y-2">
                <Label>Sort Order</Label>
                <Input
                  type="number"
                  value={formData.sort_order || 0}
                  onChange={(e) =>
                    onFormDataChange({
                      sort_order: parseInt(e.target.value) || 0,
                    })
                  }
                  placeholder="0"
                />
                <p className="text-xs text-muted-foreground">
                  Lower numbers appear first in navigation
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Page Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() =>
                  window.open(`/preview/page/${formData.slug}`, '_blank')
                }
                disabled={!formData.slug}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview Page
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => {
                  // TODO: Implement duplicate functionality
                }}
              >
                <FileText className="h-4 w-4 mr-2" />
                Duplicate Page
              </Button>

              {isEditing && (
                <Button
                  variant="outline"
                  className="w-full justify-start text-destructive hover:text-destructive"
                  onClick={() => {
                    // TODO: Implement delete functionality
                  }}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Delete Page
                </Button>
              )}
            </CardContent>
          </Card>

          {/* SEO Score */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">SEO Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Overall Score</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200"
                  >
                    85/100
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span>Title length optimal</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span>Meta description present</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <AlertTriangle className="h-3 w-3 text-yellow-600" />
                    <span>Could use more headings</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
