'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getDashboardStats, 
  getContentStatusBreakdown, 
  getRecentActivity, 
  getUpcomingPosts,
  getUserEngagementMetrics,
  getSystemHealth,
  type DashboardStats,
  type ContentStatusBreakdown,
  type RecentActivity
} from '@/lib/dashboard-helpers';
import type { Post } from '@/types';

interface DashboardData {
  stats: DashboardStats | null;
  statusBreakdown: ContentStatusBreakdown | null;
  recentActivity: RecentActivity[];
  upcomingPosts: Post[];
  userMetrics: any;
  systemHealth: any;
}

interface LoadingState {
  stats: boolean;
  statusBreakdown: boolean;
  recentActivity: boolean;
  upcomingPosts: boolean;
  userMetrics: boolean;
  systemHealth: boolean;
}

interface ErrorState {
  stats: string | null;
  statusBreakdown: string | null;
  recentActivity: string | null;
  upcomingPosts: string | null;
  userMetrics: string | null;
  systemHealth: string | null;
}

export function useDashboardData() {
  const [data, setData] = useState<DashboardData>({
    stats: null,
    statusBreakdown: null,
    recentActivity: [],
    upcomingPosts: [],
    userMetrics: null,
    systemHealth: null,
  });

  const [loading, setLoading] = useState<LoadingState>({
    stats: true,
    statusBreakdown: true,
    recentActivity: true,
    upcomingPosts: true,
    userMetrics: true,
    systemHealth: true,
  });

  const [errors, setErrors] = useState<ErrorState>({
    stats: null,
    statusBreakdown: null,
    recentActivity: null,
    upcomingPosts: null,
    userMetrics: null,
    systemHealth: null,
  });

  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Fetch dashboard statistics
  const fetchStats = useCallback(async () => {
    setLoading(prev => ({ ...prev, stats: true }));
    setErrors(prev => ({ ...prev, stats: null }));

    const result = await getDashboardStats();
    
    if (result.error) {
      setErrors(prev => ({ ...prev, stats: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, stats: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, stats: false }));
  }, []);

  // Fetch content status breakdown
  const fetchStatusBreakdown = useCallback(async () => {
    setLoading(prev => ({ ...prev, statusBreakdown: true }));
    setErrors(prev => ({ ...prev, statusBreakdown: null }));

    const result = await getContentStatusBreakdown();
    
    if (result.error) {
      setErrors(prev => ({ ...prev, statusBreakdown: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, statusBreakdown: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, statusBreakdown: false }));
  }, []);

  // Fetch recent activity
  const fetchRecentActivity = useCallback(async () => {
    setLoading(prev => ({ ...prev, recentActivity: true }));
    setErrors(prev => ({ ...prev, recentActivity: null }));

    const result = await getRecentActivity(20);
    
    if (result.error) {
      setErrors(prev => ({ ...prev, recentActivity: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, recentActivity: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, recentActivity: false }));
  }, []);

  // Fetch upcoming posts
  const fetchUpcomingPosts = useCallback(async () => {
    setLoading(prev => ({ ...prev, upcomingPosts: true }));
    setErrors(prev => ({ ...prev, upcomingPosts: null }));

    const result = await getUpcomingPosts(10);
    
    if (result.error) {
      setErrors(prev => ({ ...prev, upcomingPosts: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, upcomingPosts: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, upcomingPosts: false }));
  }, []);

  // Fetch user engagement metrics
  const fetchUserMetrics = useCallback(async () => {
    setLoading(prev => ({ ...prev, userMetrics: true }));
    setErrors(prev => ({ ...prev, userMetrics: null }));

    const result = await getUserEngagementMetrics();
    
    if (result.error) {
      setErrors(prev => ({ ...prev, userMetrics: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, userMetrics: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, userMetrics: false }));
  }, []);

  // Fetch system health
  const fetchSystemHealth = useCallback(async () => {
    setLoading(prev => ({ ...prev, systemHealth: true }));
    setErrors(prev => ({ ...prev, systemHealth: null }));

    const result = await getSystemHealth();
    
    if (result.error) {
      setErrors(prev => ({ ...prev, systemHealth: result.error! }));
    } else if (result.data) {
      setData(prev => ({ ...prev, systemHealth: result.data! }));
    }
    
    setLoading(prev => ({ ...prev, systemHealth: false }));
  }, []);

  // Refresh all data
  const refreshAll = useCallback(async () => {
    setLastRefresh(new Date());
    await Promise.all([
      fetchStats(),
      fetchStatusBreakdown(),
      fetchRecentActivity(),
      fetchUpcomingPosts(),
      fetchUserMetrics(),
      fetchSystemHealth(),
    ]);
  }, [
    fetchStats,
    fetchStatusBreakdown,
    fetchRecentActivity,
    fetchUpcomingPosts,
    fetchUserMetrics,
    fetchSystemHealth,
  ]);

  // Refresh specific data sections
  const refresh = {
    stats: fetchStats,
    statusBreakdown: fetchStatusBreakdown,
    recentActivity: fetchRecentActivity,
    upcomingPosts: fetchUpcomingPosts,
    userMetrics: fetchUserMetrics,
    systemHealth: fetchSystemHealth,
    all: refreshAll,
  };

  // Initial data fetch
  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refreshAll();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [refreshAll]);

  // Computed values
  const isLoading = Object.values(loading).some(Boolean);
  const hasErrors = Object.values(errors).some(Boolean);
  const errorMessages = Object.values(errors).filter(Boolean);

  return {
    data,
    loading,
    errors,
    isLoading,
    hasErrors,
    errorMessages,
    lastRefresh,
    refresh,
  };
}
