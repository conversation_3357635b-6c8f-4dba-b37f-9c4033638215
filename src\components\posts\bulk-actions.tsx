'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  CheckSquare, 
  Trash2, 
  Edit, 
  Copy, 
  Archive, 
  Eye, 
  Hash,
  Download,
  Upload
} from 'lucide-react';
import type { PostWithRelations, ContentStatus, Category } from '@/types';

interface BulkActionsProps {
  selectedPosts: PostWithRelations[];
  categories: Category[];
  onBulkStatusChange: (posts: PostWithRelations[], newStatus: ContentStatus) => Promise<void>;
  onBulkCategoryChange: (posts: PostWithRelations[], categoryId: string) => Promise<void>;
  onBulkDelete: (posts: PostWithRelations[]) => Promise<void>;
  onBulkDuplicate: (posts: PostWithRelations[]) => Promise<void>;
  onBulkExport: (posts: PostWithRelations[], format: 'csv' | 'json') => Promise<void>;
  onClearSelection: () => void;
  isLoading?: boolean;
}

export function BulkActions({
  selectedPosts,
  categories,
  onBulkStatusChange,
  onBulkCategoryChange,
  onBulkDelete,
  onBulkDuplicate,
  onBulkExport,
  onClearSelection,
  isLoading,
}: BulkActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');

  if (selectedPosts.length === 0) {
    return null;
  }

  const handleStatusChange = async (newStatus: ContentStatus) => {
    await onBulkStatusChange(selectedPosts, newStatus);
    setSelectedAction('');
  };

  const handleCategoryChange = async (categoryId: string) => {
    await onBulkCategoryChange(selectedPosts, categoryId);
    setSelectedAction('');
  };

  const handleDelete = async () => {
    await onBulkDelete(selectedPosts);
    setShowDeleteDialog(false);
  };

  const handleDuplicate = async () => {
    await onBulkDuplicate(selectedPosts);
    setShowDuplicateDialog(false);
  };

  const handleExport = async (format: 'csv' | 'json') => {
    await onBulkExport(selectedPosts, format);
  };

  const getStatusCounts = () => {
    const counts = selectedPosts.reduce((acc, post) => {
      acc[post.status] = (acc[post.status] || 0) + 1;
      return acc;
    }, {} as Record<ContentStatus, number>);
    
    return counts;
  };

  const statusCounts = getStatusCounts();

  return (
    <>
      <Card className="border-blue-200 bg-blue-50/50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Bulk Actions
                <Badge variant="secondary">{selectedPosts.length} selected</Badge>
              </CardTitle>
              <CardDescription>
                Perform actions on {selectedPosts.length} selected post{selectedPosts.length !== 1 ? 's' : ''}
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onClearSelection}>
              Clear Selection
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Status Overview */}
          <div className="flex flex-wrap gap-2">
            {Object.entries(statusCounts).map(([status, count]) => (
              <Badge key={status} variant="outline" className="text-xs">
                {status}: {count}
              </Badge>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            {/* Status Change */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Change Status</label>
              <Select
                value={selectedAction === 'status' ? '' : ''}
                onValueChange={(value) => {
                  setSelectedAction('status');
                  handleStatusChange(value as ContentStatus);
                }}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">
                    <div className="flex items-center gap-2">
                      <Edit className="h-4 w-4" />
                      Draft
                    </div>
                  </SelectItem>
                  <SelectItem value="pending_review">
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Pending Review
                    </div>
                  </SelectItem>
                  <SelectItem value="published">
                    <div className="flex items-center gap-2">
                      <CheckSquare className="h-4 w-4" />
                      Published
                    </div>
                  </SelectItem>
                  <SelectItem value="archived">
                    <div className="flex items-center gap-2">
                      <Archive className="h-4 w-4" />
                      Archived
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Category Change */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Change Category</label>
              <Select
                value={selectedAction === 'category' ? '' : ''}
                onValueChange={(value) => {
                  setSelectedAction('category');
                  handleCategoryChange(value);
                }}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        <Hash className="h-4 w-4" />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Export Actions */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Export</label>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('csv')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Download className="h-3 w-3 mr-1" />
                  CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('json')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Download className="h-3 w-3 mr-1" />
                  JSON
                </Button>
              </div>
            </div>

            {/* Destructive Actions */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Actions</label>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDuplicateDialog(true)}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Duplicate
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => setShowDeleteDialog(true)}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="text-xs text-muted-foreground">
            Selected posts: {selectedPosts.map(p => p.title).join(', ').substring(0, 100)}
            {selectedPosts.map(p => p.title).join(', ').length > 100 && '...'}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {selectedPosts.length} Post{selectedPosts.length !== 1 ? 's' : ''}?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete the selected posts. This action cannot be undone.
              
              <div className="mt-3 p-3 bg-muted rounded-md">
                <div className="text-sm font-medium mb-2">Posts to be deleted:</div>
                <ul className="text-sm space-y-1">
                  {selectedPosts.slice(0, 5).map((post) => (
                    <li key={post.id} className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {post.status}
                      </Badge>
                      {post.title}
                    </li>
                  ))}
                  {selectedPosts.length > 5 && (
                    <li className="text-muted-foreground">
                      ...and {selectedPosts.length - 5} more
                    </li>
                  )}
                </ul>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete {selectedPosts.length} Post{selectedPosts.length !== 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Duplicate Confirmation Dialog */}
      <AlertDialog open={showDuplicateDialog} onOpenChange={setShowDuplicateDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Duplicate {selectedPosts.length} Post{selectedPosts.length !== 1 ? 's' : ''}?</AlertDialogTitle>
            <AlertDialogDescription>
              This will create copies of the selected posts with "(Copy)" appended to their titles. 
              All duplicated posts will be created as drafts.
              
              <div className="mt-3 p-3 bg-muted rounded-md">
                <div className="text-sm font-medium mb-2">Posts to be duplicated:</div>
                <ul className="text-sm space-y-1">
                  {selectedPosts.slice(0, 5).map((post) => (
                    <li key={post.id} className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {post.status}
                      </Badge>
                      {post.title}
                    </li>
                  ))}
                  {selectedPosts.length > 5 && (
                    <li className="text-muted-foreground">
                      ...and {selectedPosts.length - 5} more
                    </li>
                  )}
                </ul>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDuplicate}>
              Duplicate {selectedPosts.length} Post{selectedPosts.length !== 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
