-- =============================================================================
-- FSNC Dashboard Database Setup - Table Definitions
-- =============================================================================
-- This script creates all database tables with proper constraints, indexes,
-- and relationships for the FSNC Dashboard application.
--
-- Run this script after 02_enums.sql
-- =============================================================================

-- =============================================================================
-- PROFILES TABLE
-- =============================================================================
-- Extends auth.users with application-specific user data
-- This table has a 1:1 relationship with auth.users

CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email CITEXT NOT NULL UNIQUE,
  full_name TEXT,
  role user_role NOT NULL DEFAULT 'member',
  avatar_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE profiles ADD CONSTRAINT profiles_email_format 
  CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add indexes
CREATE INDEX idx_profiles_email ON profiles(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_profiles_role ON profiles(role) WHERE deleted_at IS NULL;
CREATE INDEX idx_profiles_deleted_at ON profiles(deleted_at);

-- Add comments
COMMENT ON TABLE profiles IS 'User profiles extending auth.users with application-specific data';
COMMENT ON COLUMN profiles.id IS 'References auth.users.id';
COMMENT ON COLUMN profiles.role IS 'User role determining system permissions';

-- =============================================================================
-- INVITATIONS TABLE
-- =============================================================================
-- Manages the invite-only user registration system

CREATE TABLE invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email CITEXT NOT NULL,
  token TEXT NOT NULL UNIQUE DEFAULT generate_secure_token(32),
  role_to_assign user_role NOT NULL DEFAULT 'member',
  invited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  status invitation_status NOT NULL DEFAULT 'pending',
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  resent_at TIMESTAMPTZ,
  resend_count INTEGER NOT NULL DEFAULT 0
);

-- Enable RLS
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE invitations ADD CONSTRAINT invitations_email_format 
  CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
ALTER TABLE invitations ADD CONSTRAINT invitations_expires_future 
  CHECK (expires_at > created_at);
ALTER TABLE invitations ADD CONSTRAINT invitations_resend_count_positive 
  CHECK (resend_count >= 0);

-- Add indexes
CREATE INDEX idx_invitations_email ON invitations(email);
CREATE INDEX idx_invitations_token ON invitations(token);
CREATE INDEX idx_invitations_status ON invitations(status);
CREATE INDEX idx_invitations_expires_at ON invitations(expires_at);
CREATE INDEX idx_invitations_invited_by_id ON invitations(invited_by_id);

-- Add comments
COMMENT ON TABLE invitations IS 'Manages invite-only user registration system';
COMMENT ON COLUMN invitations.token IS 'Secure token for invitation validation';
COMMENT ON COLUMN invitations.expires_at IS 'Invitation expiration timestamp';

-- =============================================================================
-- CATEGORIES TABLE
-- =============================================================================
-- Content categorization for posts

CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);

-- Enable RLS
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE categories ADD CONSTRAINT categories_name_not_empty 
  CHECK (LENGTH(TRIM(name)) > 0);
ALTER TABLE categories ADD CONSTRAINT categories_slug_format 
  CHECK (slug ~* '^[a-z0-9-]+$');

-- Add indexes
CREATE UNIQUE INDEX idx_categories_slug ON categories(slug) WHERE deleted_at IS NULL;
CREATE INDEX idx_categories_name ON categories(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);

-- Add comments
COMMENT ON TABLE categories IS 'Content categories for organizing posts';
COMMENT ON COLUMN categories.slug IS 'URL-friendly category identifier';

-- =============================================================================
-- TAGS TABLE
-- =============================================================================
-- Content tagging system for posts

CREATE TABLE tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);

-- Enable RLS
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE tags ADD CONSTRAINT tags_name_not_empty 
  CHECK (LENGTH(TRIM(name)) > 0);
ALTER TABLE tags ADD CONSTRAINT tags_slug_format 
  CHECK (slug ~* '^[a-z0-9-]+$');

-- Add indexes
CREATE UNIQUE INDEX idx_tags_slug ON tags(slug) WHERE deleted_at IS NULL;
CREATE INDEX idx_tags_name ON tags(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_tags_deleted_at ON tags(deleted_at);

-- Add comments
COMMENT ON TABLE tags IS 'Content tags for organizing and filtering posts';
COMMENT ON COLUMN tags.slug IS 'URL-friendly tag identifier';

-- =============================================================================
-- POSTS TABLE
-- =============================================================================
-- Blog posts with rich content and metadata

CREATE TABLE posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  content JSONB,
  excerpt TEXT,
  featured_image_url TEXT,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  status content_status NOT NULL DEFAULT 'draft',
  published_at TIMESTAMPTZ,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  meta_title TEXT,
  meta_description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  current_version INTEGER NOT NULL DEFAULT 1,
  last_edited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- Enable RLS
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE posts ADD CONSTRAINT posts_title_not_empty 
  CHECK (LENGTH(TRIM(title)) > 0);
ALTER TABLE posts ADD CONSTRAINT posts_slug_not_empty 
  CHECK (LENGTH(TRIM(slug)) > 0);
ALTER TABLE posts ADD CONSTRAINT posts_published_at_when_published 
  CHECK ((status = 'published' AND published_at IS NOT NULL) OR status != 'published');
ALTER TABLE posts ADD CONSTRAINT posts_current_version_positive 
  CHECK (current_version > 0);

-- Add indexes
CREATE INDEX idx_posts_slug ON posts(slug) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_author_id ON posts(author_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_status ON posts(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_published_at ON posts(published_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_category_id ON posts(category_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_created_at ON posts(created_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_posts_deleted_at ON posts(deleted_at);

-- Add comments
COMMENT ON TABLE posts IS 'Blog posts with rich content and metadata';
COMMENT ON COLUMN posts.content IS 'Rich text content stored as JSON';
COMMENT ON COLUMN posts.current_version IS 'Current version number for content versioning';

-- =============================================================================
-- POST_TAGS TABLE
-- =============================================================================
-- Many-to-many relationship between posts and tags

CREATE TABLE post_tags (
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, tag_id)
);

-- Enable RLS
ALTER TABLE post_tags ENABLE ROW LEVEL SECURITY;

-- Add indexes
CREATE INDEX idx_post_tags_post_id ON post_tags(post_id);
CREATE INDEX idx_post_tags_tag_id ON post_tags(tag_id);

-- Add comments
COMMENT ON TABLE post_tags IS 'Many-to-many relationship between posts and tags';

-- =============================================================================
-- POST_VERSIONS TABLE
-- =============================================================================
-- Version history for posts to track changes over time

CREATE TABLE post_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  content JSONB,
  excerpt TEXT,
  featured_image_url TEXT,
  status content_status NOT NULL,
  published_at TIMESTAMPTZ,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  meta_title TEXT,
  meta_description TEXT,
  edited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  reason_for_change TEXT
);

-- Enable RLS
ALTER TABLE post_versions ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE post_versions ADD CONSTRAINT post_versions_version_positive
  CHECK (version_number > 0);
ALTER TABLE post_versions ADD CONSTRAINT post_versions_unique_version
  UNIQUE (post_id, version_number);

-- Add indexes
CREATE INDEX idx_post_versions_post_id ON post_versions(post_id);
CREATE INDEX idx_post_versions_version_number ON post_versions(post_id, version_number);
CREATE INDEX idx_post_versions_created_at ON post_versions(created_at);

-- Add comments
COMMENT ON TABLE post_versions IS 'Version history for posts tracking changes over time';
COMMENT ON COLUMN post_versions.reason_for_change IS 'Optional reason for the version change';

-- =============================================================================
-- PAGES TABLE
-- =============================================================================
-- Static website pages with rich content

CREATE TABLE pages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  content JSONB,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  status content_status NOT NULL DEFAULT 'draft',
  published_at TIMESTAMPTZ,
  meta_title TEXT,
  meta_description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  current_version INTEGER NOT NULL DEFAULT 1,
  last_edited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- Enable RLS
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE pages ADD CONSTRAINT pages_title_not_empty
  CHECK (LENGTH(TRIM(title)) > 0);
ALTER TABLE pages ADD CONSTRAINT pages_slug_not_empty
  CHECK (LENGTH(TRIM(slug)) > 0);
ALTER TABLE pages ADD CONSTRAINT pages_published_at_when_published
  CHECK ((status = 'published' AND published_at IS NOT NULL) OR status != 'published');
ALTER TABLE pages ADD CONSTRAINT pages_current_version_positive
  CHECK (current_version > 0);

-- Add indexes
CREATE UNIQUE INDEX idx_pages_slug ON pages(slug) WHERE deleted_at IS NULL;
CREATE INDEX idx_pages_author_id ON pages(author_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_pages_status ON pages(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_pages_published_at ON pages(published_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_pages_created_at ON pages(created_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_pages_deleted_at ON pages(deleted_at);

-- Add comments
COMMENT ON TABLE pages IS 'Static website pages with rich content';
COMMENT ON COLUMN pages.content IS 'Rich text content stored as JSON';

-- =============================================================================
-- BIOGRAPHIES TABLE
-- =============================================================================
-- Team member and featured individual profiles

CREATE TABLE biographies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  full_name TEXT NOT NULL,
  title_role TEXT,
  bio_text JSONB,
  profile_image_url TEXT,
  social_links JSONB,
  display_order INTEGER,
  is_public BOOLEAN NOT NULL DEFAULT true,
  created_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  current_version INTEGER NOT NULL DEFAULT 1,
  last_edited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- Enable RLS
ALTER TABLE biographies ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE biographies ADD CONSTRAINT biographies_full_name_not_empty
  CHECK (LENGTH(TRIM(full_name)) > 0);
ALTER TABLE biographies ADD CONSTRAINT biographies_current_version_positive
  CHECK (current_version > 0);

-- Add indexes
CREATE INDEX idx_biographies_full_name ON biographies(full_name) WHERE deleted_at IS NULL;
CREATE INDEX idx_biographies_display_order ON biographies(display_order) WHERE deleted_at IS NULL AND is_public = true;
CREATE INDEX idx_biographies_is_public ON biographies(is_public) WHERE deleted_at IS NULL;
CREATE INDEX idx_biographies_created_by_id ON biographies(created_by_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_biographies_deleted_at ON biographies(deleted_at);

-- Add comments
COMMENT ON TABLE biographies IS 'Team member and featured individual profiles';
COMMENT ON COLUMN biographies.bio_text IS 'Rich text biography content stored as JSON';
COMMENT ON COLUMN biographies.social_links IS 'Social media links stored as JSON object';

-- =============================================================================
-- NEWSLETTER_SUBSCRIBERS TABLE
-- =============================================================================
-- Newsletter subscription management with double opt-in support

CREATE TABLE newsletter_subscribers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email CITEXT NOT NULL UNIQUE,
  first_name TEXT,
  last_name TEXT,
  platform TEXT,
  is_active BOOLEAN NOT NULL DEFAULT false,
  confirmation_token TEXT UNIQUE DEFAULT generate_secure_token(32),
  confirmation_token_expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours'),
  confirmed_at TIMESTAMPTZ,
  subscribed_on_platform_at TIMESTAMPTZ,
  source TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  opt_out_at TIMESTAMPTZ
);

-- Enable RLS
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE newsletter_subscribers ADD CONSTRAINT newsletter_subscribers_email_format
  CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
ALTER TABLE newsletter_subscribers ADD CONSTRAINT newsletter_subscribers_confirmed_when_active
  CHECK ((is_active = true AND confirmed_at IS NOT NULL) OR is_active = false);

-- Add indexes
CREATE UNIQUE INDEX idx_newsletter_subscribers_email ON newsletter_subscribers(email);
CREATE INDEX idx_newsletter_subscribers_is_active ON newsletter_subscribers(is_active);
CREATE INDEX idx_newsletter_subscribers_confirmation_token ON newsletter_subscribers(confirmation_token);
CREATE INDEX idx_newsletter_subscribers_created_at ON newsletter_subscribers(created_at);

-- Add comments
COMMENT ON TABLE newsletter_subscribers IS 'Newsletter subscription management with double opt-in';
COMMENT ON COLUMN newsletter_subscribers.confirmation_token IS 'Token for email confirmation';
COMMENT ON COLUMN newsletter_subscribers.is_active IS 'Whether subscription is confirmed and active';

-- =============================================================================
-- ORGANIZATION_SETTINGS TABLE
-- =============================================================================
-- Key-value store for global application settings

CREATE TABLE organization_settings (
  key TEXT PRIMARY KEY,
  value JSONB,
  description TEXT,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE organization_settings ENABLE ROW LEVEL SECURITY;

-- Add constraints
ALTER TABLE organization_settings ADD CONSTRAINT organization_settings_key_not_empty
  CHECK (LENGTH(TRIM(key)) > 0);

-- Add indexes
CREATE INDEX idx_organization_settings_updated_at ON organization_settings(updated_at);

-- Add comments
COMMENT ON TABLE organization_settings IS 'Key-value store for global application settings';
COMMENT ON COLUMN organization_settings.value IS 'Setting value stored as JSON for flexibility';

-- =============================================================================
-- AUDIT_LOG TABLE
-- =============================================================================
-- Comprehensive audit trail for system events and user actions

CREATE TABLE audit_log (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  action audit_action NOT NULL,
  target_table_name TEXT,
  target_record_id TEXT,
  old_value JSONB,
  new_value JSONB,
  description TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Add indexes
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_target_table ON audit_log(target_table_name);
CREATE INDEX idx_audit_log_target_record ON audit_log(target_record_id);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);

-- Add comments
COMMENT ON TABLE audit_log IS 'Comprehensive audit trail for system events and user actions';
COMMENT ON COLUMN audit_log.target_record_id IS 'ID of the affected record (stored as text for flexibility)';
COMMENT ON COLUMN audit_log.old_value IS 'Previous values before change (JSON)';
COMMENT ON COLUMN audit_log.new_value IS 'New values after change (JSON)';

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Database tables created successfully at %', NOW();
END $$;
