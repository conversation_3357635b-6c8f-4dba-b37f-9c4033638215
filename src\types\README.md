# Database Types Documentation

This directory contains comprehensive TypeScript type definitions for the FSNC Dashboard database schema. These types provide full type safety when working with Supabase and ensure consistency across the application.

## Files Overview

- `database.ts` - Core database types, interfaces, and enums
- `index.ts` - Centralized exports and additional utility types
- `README.md` - This documentation file

## Database Schema Overview

The database consists of the following main entities:

### Core Tables

- **profiles** - User profiles and authentication data
- **posts** - Blog posts with versioning support
- **pages** - Static pages content
- **categories** - Post categorization
- **tags** - Post tagging system
- **biographies** - Team member biographies

### Supporting Tables

- **post_tags** - Many-to-many relationship between posts and tags
- **post_versions** - Version history for posts
- **invitations** - User invitation system
- **newsletter_subscribers** - Newsletter subscription management
- **audit_log** - System audit trail
- **organization_settings** - Application configuration

## Type Categories

### 1. Enums

```typescript
import { AuditAction, ContentStatus, UserRole } from '@/types';

// Usage examples
const status: ContentStatus = 'published';
const role: UserRole = 'editor';
const action: AuditAction = 'UPDATE';
```

### 2. Base Interfaces

Each database table has a corresponding TypeScript interface:

```typescript
import { Post, Profile, Category } from '@/types';

// These interfaces match the database schema exactly
const post: Post = {
  id: 'uuid-here',
  title: 'My Blog Post',
  slug: 'my-blog-post',
  content: { /* rich text content */ },
  status: 'published',
  author_id: 'author-uuid',
  // ... other fields
};
```

### 3. Relationship Types

For queries that include related data:

```typescript
import { PostWithRelations, BiographyWithRelations } from '@/types';

// Post with author, category, and tags included
const postWithData: PostWithRelations = {
  id: 'uuid',
  title: 'My Post',
  // ... other post fields
  author: {
    id: 'author-uuid',
    full_name: 'John Doe',
    email: '<EMAIL>',
    // ... other profile fields
  },
  category: {
    id: 'category-uuid',
    name: 'Technology',
    slug: 'technology',
  },
  tags: [
    { id: 'tag1-uuid', name: 'React', slug: 'react' },
    { id: 'tag2-uuid', name: 'TypeScript', slug: 'typescript' },
  ],
};
```

### 4. Insert/Update Types

For database operations:

```typescript
import { InsertPost, UpdatePost, InsertProfile } from '@/types';

// Creating a new post (omits auto-generated fields)
const newPost: InsertPost = {
  title: 'New Post',
  slug: 'new-post',
  content: { /* content */ },
  status: 'draft',
  author_id: 'author-uuid',
  // No need to include id, created_at, updated_at
};

// Updating a post (all fields optional except id)
const postUpdate: UpdatePost = {
  id: 'post-uuid',
  title: 'Updated Title', // Only include fields you want to update
  status: 'published',
};
```

### 5. Database Schema Type

For Supabase client configuration:

```typescript
import { Database } from '@/types';
import { createBrowserClient } from '@supabase/ssr';

const supabase = createBrowserClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Now you get full type safety on all database operations
const { data, error } = await supabase
  .from('posts') // ✅ Type-safe table name
  .select('*')
  .eq('status', 'published'); // ✅ Type-safe column names and values
```

## Usage Examples

### 1. Fetching Data with Relations

```typescript
import { createClient } from '@/lib/client';
import type { PostWithRelations } from '@/types';

async function getPostWithAuthor(id: string): Promise<PostWithRelations | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching post:', error);
    return null;
  }

  return data as PostWithRelations;
}
```

### 2. Creating New Records

```typescript
import { createClient } from '@/lib/client';
import type { InsertPost, Post } from '@/types';

async function createPost(postData: InsertPost): Promise<Post | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('posts')
    .insert(postData)
    .select()
    .single();

  if (error) {
    console.error('Error creating post:', error);
    return null;
  }

  return data;
}
```

### 3. Form Validation

```typescript
import { z } from 'zod';
import type { ContentStatus, UserRole } from '@/types';

// Create Zod schemas using the TypeScript types
const postSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().min(1, 'Slug is required'),
  content: z.record(z.any()).optional(),
  status: z.enum(['draft', 'pending_review', 'rejected', 'published', 'archived'] as const),
  category_id: z.string().uuid().optional(),
});

const inviteSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.enum(['admin', 'publisher', 'editor', 'member'] as const),
});
```

### 4. API Response Types

```typescript
import type { ApiResponse, PaginatedResponse, Post } from '@/types';

// Single item response
async function getPost(id: string): Promise<ApiResponse<Post>> {
  try {
    const post = await fetchPostFromDatabase(id);
    return {
      success: true,
      data: post,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Paginated response
async function getPosts(page: number = 1): Promise<PaginatedResponse<Post>> {
  const limit = 10;
  const posts = await fetchPostsFromDatabase(page, limit);
  const total = await getPostCount();
  
  return {
    success: true,
    data: posts,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    },
  };
}
```

## Best Practices

1. **Always use the typed Supabase client** - Import the `Database` type and use it with your Supabase client
2. **Use relationship types for complex queries** - Use `PostWithRelations`, `BiographyWithRelations`, etc. when fetching related data
3. **Use Insert/Update types for mutations** - Use `InsertPost`, `UpdatePost`, etc. for database operations
4. **Leverage TypeScript's type narrowing** - Use type guards and discriminated unions where appropriate
5. **Keep types in sync** - If you modify the database schema, regenerate these types

## Regenerating Types

When the database schema changes, you can regenerate these types by:

1. Using the Supabase CLI: `supabase gen types typescript --project-id YOUR_PROJECT_ID`
2. Or manually updating the types based on schema changes
3. Or using the MCP Supabase tool to query the latest schema

## Type Safety Benefits

- **Compile-time error checking** - Catch database-related errors before runtime
- **IntelliSense support** - Get autocomplete for table names, column names, and values
- **Refactoring safety** - Rename fields with confidence across the entire codebase
- **Documentation** - Types serve as living documentation of your database schema
- **Consistency** - Ensure consistent data structures across components and API routes
