'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  History, 
  Eye, 
  RotateCcw, 
  GitBranch, 
  User, 
  Clock,
  FileText,
  Edit,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import type { ContentStatus } from '@/types';

interface PageVersion {
  id: string;
  version: number;
  title: string;
  content: Record<string, any>;
  status: ContentStatus;
  createdAt: Date;
  createdBy: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  changeReason?: string;
  wordCount: number;
  isCurrent: boolean;
}

interface PageVersionHistoryProps {
  pageId: string;
  currentVersion: number;
  onPreviewVersion: (versionId: string) => void;
  onRestoreVersion: (versionId: string) => void;
  onCompareVersions: (version1: string, version2: string) => void;
}

// Mock data - in a real implementation, this would come from your database
const mockVersions: PageVersion[] = [
  {
    id: 'v4',
    version: 4,
    title: 'About Our Company - Updated',
    content: { type: 'doc', content: [] },
    status: 'published',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    createdBy: {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    changeReason: 'Updated company mission statement',
    wordCount: 847,
    isCurrent: true,
  },
  {
    id: 'v3',
    version: 3,
    title: 'About Our Company',
    content: { type: 'doc', content: [] },
    status: 'published',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    createdBy: {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    changeReason: 'Added team photos and bios',
    wordCount: 723,
    isCurrent: false,
  },
  {
    id: 'v2',
    version: 2,
    title: 'About Our Company',
    content: { type: 'doc', content: [] },
    status: 'published',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    createdBy: {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    changeReason: 'Fixed typos and improved formatting',
    wordCount: 692,
    isCurrent: false,
  },
  {
    id: 'v1',
    version: 1,
    title: 'About Our Company',
    content: { type: 'doc', content: [] },
    status: 'published',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    createdBy: {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    changeReason: 'Initial version',
    wordCount: 654,
    isCurrent: false,
  },
];

export function PageVersionHistory({ 
  pageId, 
  currentVersion, 
  onPreviewVersion, 
  onRestoreVersion, 
  onCompareVersions 
}: PageVersionHistoryProps) {
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  
  const versions = mockVersions; // In real implementation, fetch based on pageId

  const handleVersionSelect = (versionId: string) => {
    if (selectedVersions.includes(versionId)) {
      setSelectedVersions(selectedVersions.filter(id => id !== versionId));
    } else if (selectedVersions.length < 2) {
      setSelectedVersions([...selectedVersions, versionId]);
    } else {
      // Replace the first selected version
      setSelectedVersions([selectedVersions[1], versionId]);
    }
  };

  const getStatusBadge = (status: ContentStatus) => {
    const variants = {
      draft: 'bg-gray-100 text-gray-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-red-100 text-red-800',
      rejected: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={variants[status] || variants.draft}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const getWordCountChange = (currentCount: number, previousCount?: number) => {
    if (!previousCount) return null;
    
    const change = currentCount - previousCount;
    const isIncrease = change > 0;
    
    return (
      <span className={`text-xs ${isIncrease ? 'text-green-600' : 'text-red-600'}`}>
        {isIncrease ? '+' : ''}{change} words
      </span>
    );
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <History className="h-6 w-6" />
            Version History
          </h2>
          <p className="text-muted-foreground">
            Track changes and restore previous versions of your page
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedVersions.length === 2 && (
            <Button
              onClick={() => onCompareVersions(selectedVersions[0], selectedVersions[1])}
            >
              <GitBranch className="h-4 w-4 mr-2" />
              Compare Versions
            </Button>
          )}
        </div>
      </div>

      {/* Instructions */}
      {selectedVersions.length === 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-blue-800 font-medium">How to use version history:</p>
                <ul className="text-blue-700 text-sm mt-1 space-y-1">
                  <li>• Click on any version to select it for comparison</li>
                  <li>• Select two versions to compare their differences</li>
                  <li>• Use the restore button to revert to a previous version</li>
                  <li>• Preview any version to see how it looked</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Version List */}
      <div className="space-y-4">
        {versions.map((version, index) => {
          const previousVersion = versions[index + 1];
          const isSelected = selectedVersions.includes(version.id);
          
          return (
            <Card 
              key={version.id} 
              className={`transition-all cursor-pointer ${
                isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'
              } ${version.isCurrent ? 'border-green-200 bg-green-50' : ''}`}
              onClick={() => handleVersionSelect(version.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-xs font-medium text-blue-700">
                            v{version.version}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-semibold">{version.title}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {formatDistanceToNow(version.createdAt)} ago
                            <span>•</span>
                            {format(version.createdAt, 'MMM d, yyyy \'at\' h:mm a')}
                          </div>
                        </div>
                      </div>
                      
                      {version.isCurrent && (
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Current
                        </Badge>
                      )}
                      
                      {getStatusBadge(version.status)}
                    </div>

                    <div className="flex items-center gap-4 mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xs font-medium">
                            {getUserInitials(version.createdBy.name)}
                          </span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {version.createdBy.name}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <FileText className="h-3 w-3" />
                        {version.wordCount.toLocaleString()} words
                        {getWordCountChange(version.wordCount, previousVersion?.wordCount)}
                      </div>
                    </div>

                    {version.changeReason && (
                      <p className="text-sm text-muted-foreground italic">
                        "{version.changeReason}"
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onPreviewVersion(version.id);
                      }}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    
                    {!version.isCurrent && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRestoreVersion(version.id);
                        }}
                      >
                        <RotateCcw className="h-3 w-3 mr-1" />
                        Restore
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {versions.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Version History</h3>
            <p className="text-muted-foreground">
              Version history will appear here as you make changes to your page.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
