'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RichTextEditor } from './rich-text-editor';
import { TagInput } from './tag-input';
import { MediaUpload } from './media-upload';
import { 
  Save, 
  Eye, 
  Send, 
  Clock, 
  AlertTriangle,
  Calendar,
  User,
  Hash,
  Tag,
  Image,
  FileText,
  Settings,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { generateSlug, validateSlug } from '@/lib/slug-utils';
import type { PostFormData } from '@/hooks/use-post-editor';
import type { Category, Profile, Tag as TagType, ContentStatus } from '@/types';

interface PostEditorFormProps {
  formData: PostFormData;
  categories: Category[];
  authors: Profile[];
  tags: TagType[];
  onFormDataChange: (updates: Partial<PostFormData>) => void;
  onSave: () => Promise<{ success: boolean; error?: string }>;
  onCreateTag: (name: string) => Promise<TagType | null>;
  isLoading?: boolean;
  isSaving?: boolean;
  isDirty?: boolean;
  lastSaved?: Date;
  errors?: Record<string, string>;
  isEditing?: boolean;
}

export function PostEditorForm({
  formData,
  categories,
  authors,
  tags,
  onFormDataChange,
  onSave,
  onCreateTag,
  isLoading,
  isSaving,
  isDirty,
  lastSaved,
  errors,
  isEditing,
}: PostEditorFormProps) {
  const [activeTab, setActiveTab] = useState('content');
  const [slugManuallyEdited, setSlugManuallyEdited] = useState(false);

  const handleTitleChange = (title: string) => {
    onFormDataChange({ title });
    
    // Auto-generate slug if not manually edited
    if (!slugManuallyEdited) {
      const newSlug = generateSlug(title);
      onFormDataChange({ slug: newSlug });
    }
  };

  const handleSlugChange = (slug: string) => {
    setSlugManuallyEdited(true);
    onFormDataChange({ slug });
  };

  const handlePublish = async () => {
    onFormDataChange({ status: 'published', published_at: new Date().toISOString() });
    await onSave();
  };

  const handleSaveDraft = async () => {
    onFormDataChange({ status: 'draft' });
    await onSave();
  };

  const handleSubmitForReview = async () => {
    onFormDataChange({ status: 'pending_review' });
    await onSave();
  };

  const slugValidation = validateSlug(formData.slug);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isEditing ? 'Edit Post' : 'Create New Post'}
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? 'Update your blog post' : 'Write and publish a new blog post'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {lastSaved && (
            <span className="text-sm text-muted-foreground">
              Saved {formatDistanceToNow(lastSaved, { addSuffix: true })}
            </span>
          )}
          {isDirty && (
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Unsaved changes
            </Badge>
          )}
        </div>
      </div>

      {/* Error Messages */}
      {errors && Object.keys(errors).length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Please fix the following errors:</p>
              <ul className="list-disc list-inside text-sm">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>{error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="content">
                <FileText className="h-4 w-4 mr-2" />
                Content
              </TabsTrigger>
              <TabsTrigger value="media">
                <Image className="h-4 w-4 mr-2" />
                Media
              </TabsTrigger>
              <TabsTrigger value="seo">
                <Settings className="h-4 w-4 mr-2" />
                SEO
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-6">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter post title..."
                  className={errors?.title ? 'border-destructive' : ''}
                />
                {errors?.title && (
                  <p className="text-sm text-destructive">{errors.title}</p>
                )}
              </div>

              {/* Slug */}
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleSlugChange(e.target.value)}
                  placeholder="url-friendly-slug"
                  className={errors?.slug || !slugValidation.isValid ? 'border-destructive' : ''}
                />
                {!slugValidation.isValid && (
                  <p className="text-sm text-destructive">{slugValidation.errors[0]}</p>
                )}
                {errors?.slug && (
                  <p className="text-sm text-destructive">{errors.slug}</p>
                )}
              </div>

              {/* Content Editor */}
              <div className="space-y-2">
                <Label>Content *</Label>
                <RichTextEditor
                  content={formData.content}
                  onChange={(content) => onFormDataChange({ content })}
                  placeholder="Start writing your post..."
                />
              </div>

              {/* Excerpt */}
              <div className="space-y-2">
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => onFormDataChange({ excerpt: e.target.value })}
                  placeholder="Brief summary of your post..."
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  Optional summary that appears in post listings and social media previews
                </p>
              </div>
            </TabsContent>

            <TabsContent value="media" className="space-y-6">
              {/* Featured Image */}
              <div className="space-y-2">
                <Label>Featured Image</Label>
                {formData.featured_image_url ? (
                  <div className="space-y-2">
                    <div className="aspect-video w-full max-w-md overflow-hidden rounded-lg border">
                      <img
                        src={formData.featured_image_url}
                        alt="Featured image"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onFormDataChange({ featured_image_url: '' })}
                      >
                        Remove
                      </Button>
                      <Input
                        value={formData.featured_image_url}
                        onChange={(e) => onFormDataChange({ featured_image_url: e.target.value })}
                        placeholder="Image URL..."
                        className="flex-1"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Input
                      value={formData.featured_image_url}
                      onChange={(e) => onFormDataChange({ featured_image_url: e.target.value })}
                      placeholder="Enter image URL or upload below..."
                    />
                  </div>
                )}
              </div>

              {/* Media Upload */}
              <MediaUpload
                onFileSelect={(url) => onFormDataChange({ featured_image_url: url })}
                accept="image/*"
                maxFiles={1}
              />
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              {/* Meta Title */}
              <div className="space-y-2">
                <Label htmlFor="meta_title">Meta Title</Label>
                <Input
                  id="meta_title"
                  value={formData.meta_title}
                  onChange={(e) => onFormDataChange({ meta_title: e.target.value })}
                  placeholder="SEO title (leave empty to use post title)"
                />
                <p className="text-xs text-muted-foreground">
                  {formData.meta_title.length}/60 characters (recommended)
                </p>
              </div>

              {/* Meta Description */}
              <div className="space-y-2">
                <Label htmlFor="meta_description">Meta Description</Label>
                <Textarea
                  id="meta_description"
                  value={formData.meta_description}
                  onChange={(e) => onFormDataChange({ meta_description: e.target.value })}
                  placeholder="SEO description (leave empty to use excerpt)"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.meta_description.length}/160 characters (recommended)
                </p>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Post Preview</CardTitle>
                  <CardDescription>
                    How your post will appear to readers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <h1>{formData.title || 'Untitled Post'}</h1>
                    {formData.excerpt && (
                      <p className="lead text-muted-foreground">{formData.excerpt}</p>
                    )}
                    {formData.featured_image_url && (
                      <img
                        src={formData.featured_image_url}
                        alt="Featured"
                        className="w-full rounded-lg"
                      />
                    )}
                    <div className="text-sm text-muted-foreground">
                      Content preview would appear here...
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Publish</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-col gap-2">
                <Button
                  onClick={handlePublish}
                  disabled={isSaving || isLoading}
                  className="w-full"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  Publish
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleSaveDraft}
                  disabled={isSaving || isLoading}
                  className="w-full"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Draft
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleSubmitForReview}
                  disabled={isSaving || isLoading}
                  className="w-full"
                >
                  Submit for Review
                </Button>
              </div>
              
              <Separator />
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <Badge variant="outline">{formData.status}</Badge>
                </div>
                {formData.published_at && (
                  <div className="flex justify-between">
                    <span>Published:</span>
                    <span className="text-muted-foreground">
                      {new Date(formData.published_at).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Author */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <User className="h-4 w-4" />
                Author
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={formData.author_id}
                onValueChange={(value) => onFormDataChange({ author_id: value })}
              >
                <SelectTrigger className={errors?.author_id ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select author" />
                </SelectTrigger>
                <SelectContent>
                  {authors.map((author) => (
                    <SelectItem key={author.id} value={author.id}>
                      {author.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors?.author_id && (
                <p className="text-sm text-destructive mt-1">{errors.author_id}</p>
              )}
            </CardContent>
          </Card>

          {/* Category */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Category
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={formData.category_id}
                onValueChange={(value) => onFormDataChange({ category_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No category</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TagInput
                selectedTags={formData.tags}
                availableTags={tags}
                onTagsChange={(tags) => onFormDataChange({ tags })}
                onCreateTag={onCreateTag}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

