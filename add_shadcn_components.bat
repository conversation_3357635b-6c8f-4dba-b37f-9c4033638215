@echo off
echo Adding Shadcn/UI components...

npx shadcn@latest add button
npx shadcn@latest add input
npx shadcn@latest add label
npx shadcn@latest add textarea
npx shadcn@latest add select
npx shadcn@latest add checkbox
npx shadcn@latest add radio-group
npx shadcn@latest add switch
npx shadcn@latest add form
npx shadcn@latest add table
npx shadcn@latest add dropdown-menu
npx shadcn@latest add popover
npx shadcn@latest add dialog
npx shadcn@latest add alert-dialog
npx shadcn@latest add sheet
npx shadcn@latest add card
npx shadcn@latest add avatar
npx shadcn@latest add badge
npx shadcn@latest add alert
npx shadcn@latest add sonner # (Toaster component, often used with toast)
npx shadcn@latest add command # (For command palette/search UIs)
npx shadcn@latest add navigation-menu # (For complex navigation if needed)
npx shadcn@latest add menubar # (For traditional desktop-style menu bars)
npx shadcn@latest add context-menu
npx shadcn@latest add tooltip
npx shadcn@latest add accordion
npx shadcn@latest add collapsible
npx shadcn@latest add separator
npx shadcn@latest add progress
npx shadcn@latest add skeleton
npx shadcn@latest add tabs
npx shadcn@latest add hover-card
npx shadcn@latest add calendar # (Core for date picker)
npx shadcn@latest add slider
npx shadcn@latest add aspect-ratio
npx shadcn@latest add scroll-area
npx shadcn@latest add resizable # (For creating resizable panel layouts)

echo All listed Shadcn/UI components have been processed.


# npx shadcn@latest add button input label textarea select checkbox radio-group switch form table dropdown-menu popover dialog alert-dialog sheet card avatar badge alert sonner command navigation-menu menubar context-menu tooltip accordion collapsible separator progress skeleton tabs hover-card calendar slider aspect-ratio scroll-area resizable