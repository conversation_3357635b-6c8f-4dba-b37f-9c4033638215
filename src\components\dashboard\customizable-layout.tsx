'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { 
  Layout, 
  Settings, 
  Eye, 
  EyeOff, 
  RotateCcw,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { useState } from 'react';

interface Widget {
  id: string;
  title: string;
  component: React.ReactNode;
  defaultSize?: number;
  minSize?: number;
  visible: boolean;
  collapsible?: boolean;
}

interface CustomizableLayoutProps {
  widgets: Widget[];
  onLayoutChange?: (layout: any) => void;
  onWidgetToggle?: (widgetId: string, visible: boolean) => void;
}

export function CustomizableLayout({ 
  widgets, 
  onLayoutChange, 
  onWidgetToggle 
}: CustomizableLayoutProps) {
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [collapsedWidgets, setCollapsedWidgets] = useState<Set<string>>(new Set());

  const visibleWidgets = widgets.filter(widget => widget.visible);

  const toggleWidgetCollapse = (widgetId: string) => {
    setCollapsedWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      return newSet;
    });
  };

  const resetLayout = () => {
    setCollapsedWidgets(new Set());
    // Reset all widgets to visible
    widgets.forEach(widget => {
      if (onWidgetToggle) {
        onWidgetToggle(widget.id, true);
      }
    });
  };

  if (visibleWidgets.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layout className="h-5 w-5" />
            Dashboard Layout
          </CardTitle>
          <CardDescription>
            No widgets are currently visible. Enable widgets to customize your dashboard.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setIsCustomizing(true)}>
            Customize Layout
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Layout Controls */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base flex items-center gap-2">
                <Layout className="h-4 w-4" />
                Dashboard Layout
              </CardTitle>
              <CardDescription className="text-sm">
                Customize your dashboard layout and widget visibility
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {visibleWidgets.length} widgets
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomizing(!isCustomizing)}
                className="flex items-center gap-2"
              >
                <Settings className="h-3 w-3" />
                {isCustomizing ? 'Done' : 'Customize'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetLayout}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-3 w-3" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {isCustomizing && (
          <CardContent className="pt-0">
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Widget Visibility</h4>
              <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                {widgets.map(widget => (
                  <div key={widget.id} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm">{widget.title}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onWidgetToggle?.(widget.id, !widget.visible)}
                      className="flex items-center gap-1"
                    >
                      {widget.visible ? (
                        <Eye className="h-3 w-3" />
                      ) : (
                        <EyeOff className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Resizable Layout */}
      <div className="space-y-4">
        {visibleWidgets.length === 1 ? (
          // Single widget - no resizing needed
          <WidgetContainer
            widget={visibleWidgets[0]}
            isCollapsed={collapsedWidgets.has(visibleWidgets[0].id)}
            onToggleCollapse={() => toggleWidgetCollapse(visibleWidgets[0].id)}
          />
        ) : visibleWidgets.length === 2 ? (
          // Two widgets - horizontal split
          <ResizablePanelGroup direction="horizontal" className="min-h-[400px]">
            <ResizablePanel 
              defaultSize={visibleWidgets[0].defaultSize || 50}
              minSize={visibleWidgets[0].minSize || 30}
            >
              <WidgetContainer
                widget={visibleWidgets[0]}
                isCollapsed={collapsedWidgets.has(visibleWidgets[0].id)}
                onToggleCollapse={() => toggleWidgetCollapse(visibleWidgets[0].id)}
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel 
              defaultSize={visibleWidgets[1].defaultSize || 50}
              minSize={visibleWidgets[1].minSize || 30}
            >
              <WidgetContainer
                widget={visibleWidgets[1]}
                isCollapsed={collapsedWidgets.has(visibleWidgets[1].id)}
                onToggleCollapse={() => toggleWidgetCollapse(visibleWidgets[1].id)}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        ) : (
          // Multiple widgets - grid layout
          <div className="grid gap-4 lg:grid-cols-2">
            {visibleWidgets.map(widget => (
              <WidgetContainer
                key={widget.id}
                widget={widget}
                isCollapsed={collapsedWidgets.has(widget.id)}
                onToggleCollapse={() => toggleWidgetCollapse(widget.id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface WidgetContainerProps {
  widget: Widget;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

function WidgetContainer({ widget, isCollapsed, onToggleCollapse }: WidgetContainerProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{widget.title}</CardTitle>
          {widget.collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="flex items-center gap-1"
            >
              {isCollapsed ? (
                <Maximize2 className="h-3 w-3" />
              ) : (
                <Minimize2 className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardContent className="pt-0">
          {widget.component}
        </CardContent>
      )}
      
      {isCollapsed && (
        <CardContent className="pt-0">
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            <p className="text-sm">Widget collapsed</p>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

// Loading skeleton for widgets
export function WidgetSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-3 w-48" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-32 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
