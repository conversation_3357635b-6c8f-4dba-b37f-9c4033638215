'use client';

import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Search, 
  Globe, 
  FileText,
  Target,
  TrendingUp
} from 'lucide-react';

interface SEOAnalysis {
  score: number;
  checks: SEOCheck[];
  recommendations: string[];
}

interface SEOCheck {
  id: string;
  name: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  impact: 'high' | 'medium' | 'low';
}

interface SEOAnalyzerProps {
  title: string;
  slug: string;
  content: Record<string, any>;
  metaTitle?: string;
  metaDescription?: string;
}

export function SEOAnalyzer({ 
  title, 
  slug, 
  content, 
  metaTitle, 
  metaDescription 
}: SEOAnalyzerProps) {
  
  const analysis = useMemo((): SEOAnalysis => {
    const checks: SEOCheck[] = [];
    let score = 0;
    const maxScore = 100;

    // Title checks
    if (title) {
      if (title.length >= 10 && title.length <= 60) {
        checks.push({
          id: 'title-length',
          name: 'Title Length',
          status: 'pass',
          message: `Title length is optimal (${title.length} characters)`,
          impact: 'high'
        });
        score += 15;
      } else if (title.length > 0) {
        checks.push({
          id: 'title-length',
          name: 'Title Length',
          status: 'warning',
          message: `Title should be 10-60 characters (current: ${title.length})`,
          impact: 'high'
        });
        score += 8;
      } else {
        checks.push({
          id: 'title-length',
          name: 'Title Length',
          status: 'fail',
          message: 'Title is required',
          impact: 'high'
        });
      }
    }

    // Meta title checks
    if (metaTitle) {
      if (metaTitle.length >= 10 && metaTitle.length <= 60) {
        checks.push({
          id: 'meta-title',
          name: 'Meta Title',
          status: 'pass',
          message: `Meta title length is optimal (${metaTitle.length} characters)`,
          impact: 'high'
        });
        score += 15;
      } else {
        checks.push({
          id: 'meta-title',
          name: 'Meta Title',
          status: 'warning',
          message: `Meta title should be 10-60 characters (current: ${metaTitle.length})`,
          impact: 'high'
        });
        score += 8;
      }
    } else {
      checks.push({
        id: 'meta-title',
        name: 'Meta Title',
        status: 'warning',
        message: 'Meta title not set (using page title)',
        impact: 'medium'
      });
      score += 5;
    }

    // Meta description checks
    if (metaDescription) {
      if (metaDescription.length >= 120 && metaDescription.length <= 160) {
        checks.push({
          id: 'meta-description',
          name: 'Meta Description',
          status: 'pass',
          message: `Meta description length is optimal (${metaDescription.length} characters)`,
          impact: 'high'
        });
        score += 15;
      } else if (metaDescription.length > 0) {
        checks.push({
          id: 'meta-description',
          name: 'Meta Description',
          status: 'warning',
          message: `Meta description should be 120-160 characters (current: ${metaDescription.length})`,
          impact: 'high'
        });
        score += 8;
      }
    } else {
      checks.push({
        id: 'meta-description',
        name: 'Meta Description',
        status: 'fail',
        message: 'Meta description is missing',
        impact: 'high'
      });
    }

    // URL slug checks
    if (slug) {
      if (slug.length <= 50 && /^[a-z0-9-]+$/.test(slug)) {
        checks.push({
          id: 'url-slug',
          name: 'URL Slug',
          status: 'pass',
          message: 'URL slug is SEO-friendly',
          impact: 'medium'
        });
        score += 10;
      } else {
        checks.push({
          id: 'url-slug',
          name: 'URL Slug',
          status: 'warning',
          message: 'URL slug could be more SEO-friendly',
          impact: 'medium'
        });
        score += 5;
      }
    } else {
      checks.push({
        id: 'url-slug',
        name: 'URL Slug',
        status: 'fail',
        message: 'URL slug is required',
        impact: 'high'
      });
    }

    // Content analysis
    const contentText = extractTextFromContent(content);
    if (contentText) {
      const wordCount = contentText.split(/\s+/).length;
      
      if (wordCount >= 300) {
        checks.push({
          id: 'content-length',
          name: 'Content Length',
          status: 'pass',
          message: `Good content length (${wordCount} words)`,
          impact: 'medium'
        });
        score += 10;
      } else if (wordCount >= 100) {
        checks.push({
          id: 'content-length',
          name: 'Content Length',
          status: 'warning',
          message: `Content could be longer (${wordCount} words)`,
          impact: 'medium'
        });
        score += 5;
      } else {
        checks.push({
          id: 'content-length',
          name: 'Content Length',
          status: 'fail',
          message: `Content is too short (${wordCount} words)`,
          impact: 'medium'
        });
      }

      // Check for headings
      const hasHeadings = content?.content?.some((node: any) => 
        node.type === 'heading'
      );
      
      if (hasHeadings) {
        checks.push({
          id: 'headings',
          name: 'Headings Structure',
          status: 'pass',
          message: 'Page contains headings for better structure',
          impact: 'medium'
        });
        score += 10;
      } else {
        checks.push({
          id: 'headings',
          name: 'Headings Structure',
          status: 'warning',
          message: 'Consider adding headings to improve content structure',
          impact: 'medium'
        });
        score += 3;
      }
    } else {
      checks.push({
        id: 'content-length',
        name: 'Content Length',
        status: 'fail',
        message: 'No content found',
        impact: 'high'
      });
    }

    // Keyword density (simplified)
    if (title && contentText) {
      const titleWords = title.toLowerCase().split(/\s+/);
      const contentWords = contentText.toLowerCase().split(/\s+/);
      const keywordMentions = titleWords.some(word => 
        contentWords.includes(word) && word.length > 3
      );
      
      if (keywordMentions) {
        checks.push({
          id: 'keyword-usage',
          name: 'Keyword Usage',
          status: 'pass',
          message: 'Title keywords found in content',
          impact: 'medium'
        });
        score += 10;
      } else {
        checks.push({
          id: 'keyword-usage',
          name: 'Keyword Usage',
          status: 'warning',
          message: 'Consider including title keywords in content',
          impact: 'medium'
        });
        score += 3;
      }
    }

    // Generate recommendations
    const recommendations: string[] = [];
    checks.forEach(check => {
      if (check.status === 'fail' || check.status === 'warning') {
        switch (check.id) {
          case 'meta-description':
            recommendations.push('Add a compelling meta description to improve click-through rates');
            break;
          case 'content-length':
            recommendations.push('Expand your content to provide more value to readers');
            break;
          case 'headings':
            recommendations.push('Use headings (H2, H3) to structure your content better');
            break;
          case 'keyword-usage':
            recommendations.push('Include your target keywords naturally throughout the content');
            break;
        }
      }
    });

    return {
      score: Math.min(score, maxScore),
      checks,
      recommendations
    };
  }, [title, slug, content, metaTitle, metaDescription]);

  const extractTextFromContent = (content: Record<string, any>): string => {
    if (!content?.content) return '';
    
    const extractText = (nodes: any[]): string => {
      return nodes.map(node => {
        if (node.type === 'text') {
          return node.text || '';
        } else if (node.content) {
          return extractText(node.content);
        }
        return '';
      }).join(' ');
    };
    
    return extractText(content.content);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getCheckIcon = (status: 'pass' | 'warning' | 'fail') => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'fail': return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* SEO Score Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            SEO Analysis
          </CardTitle>
          <CardDescription>
            Comprehensive analysis of your page's search engine optimization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className={`text-3xl font-bold ${getScoreColor(analysis.score)}`}>
                  {analysis.score}/100
                </div>
                <p className="text-sm text-muted-foreground">SEO Score</p>
              </div>
              <Badge className={getScoreBadgeColor(analysis.score)}>
                {analysis.score >= 80 ? 'Excellent' : 
                 analysis.score >= 60 ? 'Good' : 'Needs Improvement'}
              </Badge>
            </div>
            <Progress value={analysis.score} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* SEO Checks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            SEO Checks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analysis.checks.map(check => (
              <div key={check.id} className="flex items-start gap-3 p-3 rounded-lg border">
                {getCheckIcon(check.status)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{check.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {check.impact} impact
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {check.message}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {analysis.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {analysis.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2 flex-shrink-0" />
                  {recommendation}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
