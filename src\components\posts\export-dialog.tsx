'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Download, 
  FileText, 
  Database, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import type { PostWithRelations, ContentStatus } from '@/types';

interface ExportDialogProps {
  posts: PostWithRelations[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (options: ExportOptions) => Promise<void>;
}

interface ExportOptions {
  format: 'csv' | 'json' | 'pdf';
  fields: string[];
  filters: {
    status?: ContentStatus[];
    dateRange?: {
      from: Date;
      to: Date;
    };
  };
  includeContent: boolean;
  includeMetadata: boolean;
}

export function ExportDialog({ posts, isOpen, onOpenChange, onExport }: ExportDialogProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    fields: ['title', 'status', 'author', 'created_at', 'published_at'],
    filters: {},
    includeContent: false,
    includeMetadata: true,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportComplete, setExportComplete] = useState(false);

  const availableFields = [
    { id: 'title', label: 'Title', required: true },
    { id: 'slug', label: 'Slug' },
    { id: 'excerpt', label: 'Excerpt' },
    { id: 'content', label: 'Content' },
    { id: 'status', label: 'Status', required: true },
    { id: 'author', label: 'Author' },
    { id: 'category', label: 'Category' },
    { id: 'tags', label: 'Tags' },
    { id: 'featured_image_url', label: 'Featured Image' },
    { id: 'meta_title', label: 'Meta Title' },
    { id: 'meta_description', label: 'Meta Description' },
    { id: 'created_at', label: 'Created Date', required: true },
    { id: 'updated_at', label: 'Updated Date' },
    { id: 'published_at', label: 'Published Date' },
  ];

  const handleFieldToggle = (fieldId: string, checked: boolean) => {
    const field = availableFields.find(f => f.id === fieldId);
    if (field?.required) return; // Don't allow toggling required fields

    setExportOptions(prev => ({
      ...prev,
      fields: checked 
        ? [...prev.fields, fieldId]
        : prev.fields.filter(f => f !== fieldId)
    }));
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);
    setExportComplete(false);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      await onExport(exportOptions);

      clearInterval(progressInterval);
      setExportProgress(100);
      setExportComplete(true);
      
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
        setExportComplete(false);
        onOpenChange(false);
      }, 2000);
    } catch (error) {
      setIsExporting(false);
      setExportProgress(0);
      console.error('Export failed:', error);
    }
  };

  const getFilteredPostsCount = () => {
    let filtered = posts;
    
    if (exportOptions.filters.status?.length) {
      filtered = filtered.filter(post => 
        exportOptions.filters.status!.includes(post.status)
      );
    }
    
    return filtered.length;
  };

  const filteredCount = getFilteredPostsCount();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Posts
          </DialogTitle>
          <DialogDescription>
            Export your posts in various formats with customizable options
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Export Format</CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={exportOptions.format}
                onValueChange={(value: 'csv' | 'json' | 'pdf') => 
                  setExportOptions(prev => ({ ...prev, format: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      CSV - Spreadsheet format
                    </div>
                  </SelectItem>
                  <SelectItem value="json">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      JSON - Structured data
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      PDF - Document format
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Field Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Fields to Export</CardTitle>
              <CardDescription>
                Select which fields to include in the export
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2">
                {availableFields.map((field) => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.id}
                      checked={exportOptions.fields.includes(field.id)}
                      onCheckedChange={(checked) => 
                        handleFieldToggle(field.id, checked as boolean)
                      }
                      disabled={field.required}
                    />
                    <label
                      htmlFor={field.id}
                      className={`text-sm ${field.required ? 'font-medium' : ''}`}
                    >
                      {field.label}
                      {field.required && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Required
                        </Badge>
                      )}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Content Options */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Content Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeContent"
                  checked={exportOptions.includeContent}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ 
                      ...prev, 
                      includeContent: checked as boolean 
                    }))
                  }
                />
                <label htmlFor="includeContent" className="text-sm">
                  Include full post content
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeMetadata"
                  checked={exportOptions.includeMetadata}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ 
                      ...prev, 
                      includeMetadata: checked as boolean 
                    }))
                  }
                />
                <label htmlFor="includeMetadata" className="text-sm">
                  Include SEO metadata
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Status Filter */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Filter by Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2 md:grid-cols-3">
                {(['published', 'draft', 'pending_review', 'rejected', 'archived'] as ContentStatus[]).map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={exportOptions.filters.status?.includes(status) || false}
                      onCheckedChange={(checked) => {
                        setExportOptions(prev => ({
                          ...prev,
                          filters: {
                            ...prev.filters,
                            status: checked
                              ? [...(prev.filters.status || []), status]
                              : (prev.filters.status || []).filter(s => s !== status)
                          }
                        }));
                      }}
                    />
                    <label htmlFor={`status-${status}`} className="text-sm capitalize">
                      {status.replace('_', ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Export Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Export Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total posts available:</span>
                  <Badge variant="outline">{posts.length}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Posts to export:</span>
                  <Badge variant="default">{filteredCount}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Selected fields:</span>
                  <Badge variant="outline">{exportOptions.fields.length}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Format:</span>
                  <Badge variant="secondary">{exportOptions.format.toUpperCase()}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Progress */}
          {isExporting && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {exportComplete ? 'Export Complete!' : 'Exporting...'}
                    </span>
                    {exportComplete ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    )}
                  </div>
                  <Progress value={exportProgress} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {exportComplete 
                      ? 'Your download should start automatically.'
                      : `Processing ${filteredCount} posts...`
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleExport} 
              disabled={isExporting || filteredCount === 0}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export {filteredCount} Post{filteredCount !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
