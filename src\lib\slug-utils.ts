/**
 * Utility functions for generating and validating slugs
 */

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '');
}

/**
 * Validate if a slug is properly formatted
 */
export function isValidSlug(slug: string): boolean {
  // Slug should only contain lowercase letters, numbers, and hyphens
  // Should not start or end with hyphens
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

/**
 * Clean and format a slug input
 */
export function cleanSlug(input: string): string {
  return input
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Generate a unique slug by appending a number if needed
 */
export function generateUniqueSlug(
  baseSlug: string, 
  existingSlugs: string[]
): string {
  let slug = baseSlug;
  let counter = 1;
  
  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
}

/**
 * Extract slug from a URL path
 */
export function extractSlugFromPath(path: string): string {
  const segments = path.split('/').filter(Boolean);
  return segments[segments.length - 1] || '';
}

/**
 * Validate slug length and format
 */
export function validateSlug(slug: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!slug) {
    errors.push('Slug is required');
  } else {
    if (slug.length < 3) {
      errors.push('Slug must be at least 3 characters long');
    }
    
    if (slug.length > 100) {
      errors.push('Slug must be less than 100 characters');
    }
    
    if (!isValidSlug(slug)) {
      errors.push('Slug can only contain lowercase letters, numbers, and hyphens');
    }
    
    if (slug.startsWith('-') || slug.endsWith('-')) {
      errors.push('Slug cannot start or end with hyphens');
    }
    
    if (slug.includes('--')) {
      errors.push('Slug cannot contain consecutive hyphens');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
