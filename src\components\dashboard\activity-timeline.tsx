'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  FileText, 
  Users, 
  UserPlus, 
  LogIn, 
  LogOut, 
  Edit, 
  Trash2, 
  Plus,
  Mail,
  Filter,
  RefreshCw
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import type { RecentActivity } from '@/lib/dashboard-helpers';
import { useState } from 'react';

interface ActivityTimelineProps {
  activities: RecentActivity[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

export function ActivityTimeline({ activities, isLoading, onRefresh }: ActivityTimelineProps) {
  const [filter, setFilter] = useState<string>('all');
  const [tableFilter, setTableFilter] = useState<string>('all');

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="h-10 w-10 bg-muted rounded-full"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 w-3/4 bg-muted rounded"></div>
                  <div className="h-3 w-1/2 bg-muted rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'INSERT':
        return Plus;
      case 'UPDATE':
        return Edit;
      case 'DELETE':
        return Trash2;
      case 'LOGIN_SUCCESS':
        return LogIn;
      case 'LOGIN_FAIL':
        return LogOut;
      case 'INVITE_SENT':
      case 'INVITE_ACCEPTED':
        return UserPlus;
      case 'NEWSLETTER_SUBSCRIBE_ATTEMPT':
      case 'NEWSLETTER_CONFIRMED':
      case 'NEWSLETTER_UNSUBSCRIBE':
        return Mail;
      default:
        return Activity;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'INSERT':
        return 'text-green-600 bg-green-50';
      case 'UPDATE':
        return 'text-blue-600 bg-blue-50';
      case 'DELETE':
        return 'text-red-600 bg-red-50';
      case 'LOGIN_SUCCESS':
        return 'text-green-600 bg-green-50';
      case 'LOGIN_FAIL':
        return 'text-red-600 bg-red-50';
      case 'INVITE_SENT':
      case 'INVITE_ACCEPTED':
        return 'text-purple-600 bg-purple-50';
      case 'NEWSLETTER_SUBSCRIBE_ATTEMPT':
      case 'NEWSLETTER_CONFIRMED':
      case 'NEWSLETTER_UNSUBSCRIBE':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getActionLabel = (action: string, tableName: string) => {
    const table = tableName.replace('_', ' ').toLowerCase();
    
    switch (action) {
      case 'INSERT':
        return `Created new ${table}`;
      case 'UPDATE':
        return `Updated ${table}`;
      case 'DELETE':
        return `Deleted ${table}`;
      case 'LOGIN_SUCCESS':
        return 'Logged in successfully';
      case 'LOGIN_FAIL':
        return 'Failed login attempt';
      case 'INVITE_SENT':
        return 'Sent invitation';
      case 'INVITE_ACCEPTED':
        return 'Accepted invitation';
      case 'NEWSLETTER_SUBSCRIBE_ATTEMPT':
        return 'Newsletter subscription attempt';
      case 'NEWSLETTER_CONFIRMED':
        return 'Newsletter subscription confirmed';
      case 'NEWSLETTER_UNSUBSCRIBE':
        return 'Newsletter unsubscribed';
      default:
        return `${action} on ${table}`;
    }
  };

  const filteredActivities = activities.filter(activity => {
    if (filter !== 'all' && activity.action !== filter) return false;
    if (tableFilter !== 'all' && activity.table_name !== tableFilter) return false;
    return true;
  });

  const uniqueActions = [...new Set(activities.map(a => a.action))];
  const uniqueTables = [...new Set(activities.map(a => a.table_name))];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest system activities and user actions
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
        
        {/* Filters */}
        <div className="flex gap-2">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by action" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              {uniqueActions.map(action => (
                <SelectItem key={action} value={action}>
                  {action.replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={tableFilter} onValueChange={setTableFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by table" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tables</SelectItem>
              {uniqueTables.map(table => (
                <SelectItem key={table} value={table}>
                  {table.replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {filteredActivities.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No activities found matching your filters</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActivities.map((activity, index) => {
                const ActionIcon = getActionIcon(activity.action);
                const actionColors = getActionColor(activity.action);
                
                return (
                  <div key={activity.id} className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${actionColors}`}>
                      <ActionIcon className="h-4 w-4" />
                    </div>
                    
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">
                          {getActionLabel(activity.action, activity.table_name)}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {activity.table_name}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={activity.user_avatar} />
                          <AvatarFallback className="text-xs">
                            {activity.user_name?.charAt(0) || 'S'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          {activity.user_name || 'System'}
                        </span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(activity.created_at, { addSuffix: true })}
                        </span>
                      </div>
                      
                      {activity.details && (
                        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                          {JSON.stringify(activity.details, null, 2)}
                        </div>
                      )}
                    </div>
                    
                    {index < filteredActivities.length - 1 && (
                      <Separator className="mt-4" />
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
