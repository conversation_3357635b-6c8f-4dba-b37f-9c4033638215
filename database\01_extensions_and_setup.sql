-- =============================================================================
-- FSNC Dashboard Database Setup - Extensions and Initial Configuration
-- =============================================================================
-- This script enables required PostgreSQL extensions and sets up the initial
-- database configuration for the FSNC Dashboard application.
--
-- Run this script first before any other database setup scripts.
-- =============================================================================

-- Enable required PostgreSQL extensions
-- These extensions provide additional functionality needed by the application

-- UUID generation functions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Cryptographic functions for password hashing and token generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- HTTP functions for making external API calls (useful for webhooks)
CREATE EXTENSION IF NOT EXISTS "http";

-- Additional text search capabilities
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Case-insensitive text extension
CREATE EXTENSION IF NOT EXISTS "citext";

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Function to generate secure random tokens for invitations and confirmations
CREATE OR REPLACE FUNCTION generate_secure_token(length INTEGER DEFAULT 32)
RETURNS TEXT AS $$
BEGIN
  RETURN encode(gen_random_bytes(length), 'hex');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if a user has a specific role
CREATE OR REPLACE FUNCTION user_has_role(user_id UUID, required_role TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role
  FROM profiles
  WHERE id = user_id AND deleted_at IS NULL;
  
  RETURN CASE required_role
    WHEN 'admin' THEN user_role = 'admin'
    WHEN 'publisher' THEN user_role IN ('admin', 'publisher')
    WHEN 'editor' THEN user_role IN ('admin', 'publisher', 'editor')
    WHEN 'member' THEN user_role IN ('admin', 'publisher', 'editor', 'member')
    ELSE FALSE
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's profile
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS profiles AS $$
DECLARE
  profile profiles;
BEGIN
  SELECT * INTO profile
  FROM profiles
  WHERE id = auth.uid() AND deleted_at IS NULL;
  
  RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user can access a resource
CREATE OR REPLACE FUNCTION can_access_resource(resource_owner_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  current_user_id UUID;
  current_user_role TEXT;
BEGIN
  current_user_id := auth.uid();
  
  -- No user authenticated
  IF current_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Get current user's role
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = current_user_id AND deleted_at IS NULL;
  
  -- Admin can access everything
  IF current_user_role = 'admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Publisher can access most content
  IF current_user_role = 'publisher' THEN
    RETURN TRUE;
  END IF;
  
  -- Editor can access their own content and published content
  IF current_user_role = 'editor' THEN
    RETURN resource_owner_id IS NULL OR resource_owner_id = current_user_id;
  END IF;
  
  -- Member has limited access (read-only to published content)
  IF current_user_role = 'member' THEN
    RETURN resource_owner_id IS NULL;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- CONFIGURATION SETTINGS
-- =============================================================================

-- Set timezone to UTC for consistency
SET timezone = 'UTC';

-- Enable Row Level Security by default for all tables
-- (Individual table policies will be defined in later scripts)

-- =============================================================================
-- COMMENTS AND DOCUMENTATION
-- =============================================================================

COMMENT ON EXTENSION "uuid-ossp" IS 'UUID generation functions for primary keys';
COMMENT ON EXTENSION "pgcrypto" IS 'Cryptographic functions for secure token generation';
COMMENT ON EXTENSION "http" IS 'HTTP client functions for external API calls';
COMMENT ON EXTENSION "pg_trgm" IS 'Trigram matching for improved text search';
COMMENT ON EXTENSION "citext" IS 'Case-insensitive text type for email addresses';

COMMENT ON FUNCTION generate_secure_token(INTEGER) IS 'Generates cryptographically secure random tokens';
COMMENT ON FUNCTION user_has_role(UUID, TEXT) IS 'Checks if a user has the required role or higher';
COMMENT ON FUNCTION get_current_user_profile() IS 'Returns the current authenticated user profile';
COMMENT ON FUNCTION can_access_resource(UUID) IS 'Checks if current user can access a resource based on ownership and role';

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Extensions and setup completed successfully at %', NOW();
END $$;
