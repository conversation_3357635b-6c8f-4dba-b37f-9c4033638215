'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getPages, 
  getAuthors, 
  updatePage,
  deletePage,
  duplicatePage
} from '@/lib/database-helpers';
import type { 
  PageWithRelations, 
  Profile, 
  QueryParams, 
  ContentStatus,
  PaginatedResponse 
} from '@/types';

interface PagesManagementState {
  pages: PageWithRelations[];
  authors: Profile[];
  pagination: PaginatedResponse<PageWithRelations>['pagination'];
  loading: {
    pages: boolean;
    authors: boolean;
    actions: boolean;
  };
  errors: {
    pages?: string;
    authors?: string;
    actions?: string;
  };
}

interface PagesManagementActions {
  loadPages: (params?: QueryParams) => Promise<void>;
  loadAuthors: <AUTHORS>
  updatePageStatus: (id: string, status: ContentStatus) => Promise<{ success: boolean; error?: string }>;
  deletePage: (id: string) => Promise<{ success: boolean; error?: string }>;
  duplicatePage: (id: string, newTitle?: string) => Promise<{ success: boolean; data?: any; error?: string }>;
  bulkUpdatePages: (ids: string[], updates: Partial<PageWithRelations>) => Promise<{ success: boolean; error?: string }>;
  bulkDeletePages: (ids: string[]) => Promise<{ success: boolean; error?: string }>;
  refresh: () => Promise<void>;
}

export function usePagesManagement(): PagesManagementState & PagesManagementActions {
  const [state, setState] = useState<PagesManagementState>({
    pages: [],
    authors: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false,
    },
    loading: {
      pages: false,
      authors: false,
      actions: false,
    },
    errors: {},
  });

  const [currentParams, setCurrentParams] = useState<QueryParams>({});

  const loadPages = useCallback(async (params: QueryParams = {}) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, pages: true },
      errors: { ...prev.errors, pages: undefined },
    }));

    try {
      const response = await getPages(params);
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          pages: response.data!,
          pagination: response.pagination!,
          loading: { ...prev.loading, pages: false },
        }));
        setCurrentParams(params);
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, pages: false },
          errors: { ...prev.errors, pages: response.error || 'Failed to load pages' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, pages: false },
        errors: { ...prev.errors, pages: 'An unexpected error occurred' },
      }));
    }
  }, []);

  const loadAuthors = useCallback(async () => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, authors: true },
      errors: { ...prev.errors, authors: undefined },
    }));

    try {
      const response = await getAuthors();
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          authors: response.data!,
          loading: { ...prev.loading, authors: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, authors: false },
          errors: { ...prev.errors, authors: response.error || 'Failed to load authors' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, authors: false },
        errors: { ...prev.errors, authors: 'An unexpected error occurred' },
      }));
    }
  }, []);

  const updatePageStatus = useCallback(async (id: string, status: ContentStatus) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined },
    }));

    try {
      const result = await updatePage(id, { status });
      
      if (result.data) {
        // Update the page in the local state
        setState(prev => ({
          ...prev,
          pages: prev.pages.map(page => 
            page.id === id ? { ...page, status, updated_at: new Date() } : page
          ),
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to update page status' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, []);

  const handleDeletePage = useCallback(async (id: string) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined },
    }));

    try {
      const result = await deletePage(id);
      
      if (result.success) {
        // Remove the page from the local state
        setState(prev => ({
          ...prev,
          pages: prev.pages.filter(page => page.id !== id),
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to delete page' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, []);

  const handleDuplicatePage = useCallback(async (id: string, newTitle?: string) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined },
    }));

    try {
      const result = await duplicatePage(id, newTitle);
      
      if (result.data) {
        // Refresh the pages list to include the new duplicate
        await loadPages(currentParams);
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true, data: result.data };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to duplicate page' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, [loadPages, currentParams]);

  const bulkUpdatePages = useCallback(async (ids: string[], updates: Partial<PageWithRelations>) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined },
    }));

    try {
      const results = await Promise.all(
        ids.map(id => updatePage(id, updates))
      );

      const hasErrors = results.some(result => result.error);
      
      if (!hasErrors) {
        // Update the pages in the local state
        setState(prev => ({
          ...prev,
          pages: prev.pages.map(page => 
            ids.includes(page.id) ? { ...page, ...updates, updated_at: new Date() } : page
          ),
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: 'Some pages failed to update' },
        }));
        return { success: false, error: 'Some pages failed to update' };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, []);

  const bulkDeletePages = useCallback(async (ids: string[]) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined },
    }));

    try {
      const results = await Promise.all(
        ids.map(id => deletePage(id))
      );

      const hasErrors = results.some(result => !result.success);
      
      if (!hasErrors) {
        // Remove the pages from the local state
        setState(prev => ({
          ...prev,
          pages: prev.pages.filter(page => !ids.includes(page.id)),
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: 'Some pages failed to delete' },
        }));
        return { success: false, error: 'Some pages failed to delete' };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, []);

  const refresh = useCallback(async () => {
    await Promise.all([
      loadPages(currentParams),
      loadAuthors(),
    ]);
  }, [loadPages, loadAuthors, currentParams]);

  // Load initial data
  useEffect(() => {
    loadPages();
    loadAuthors();
  }, [loadPages, loadAuthors]);

  return {
    ...state,
    loadPages,
    loadAuthors,
    updatePageStatus,
    deletePage: handleDeletePage,
    duplicatePage: handleDuplicatePage,
    bulkUpdatePages,
    bulkDeletePages,
    refresh,
  };
}
