'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Eye, 
  ExternalLink, 
  Share2, 
  Smartphone, 
  Monitor, 
  Search,
  Calendar,
  User,
  Hash,
  Tag
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import type { PostWithRelations } from '@/types';

interface PostPreviewProps {
  post: PostWithRelations;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  trigger?: React.ReactNode;
}

interface PostHoverCardProps {
  post: PostWithRelations;
  children: React.ReactNode;
}

export function PostPreview({ post, isOpen, onOpenChange, trigger }: PostPreviewProps) {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');

  const renderContent = (content: any) => {
    if (!content) return <p className="text-muted-foreground">No content available</p>;
    
    // Simple content rendering - in a real app, you'd use a proper rich text renderer
    if (typeof content === 'string') {
      return <div dangerouslySetInnerHTML={{ __html: content }} />;
    }
    
    if (content.type === 'doc' && content.content) {
      return (
        <div className="prose prose-sm max-w-none">
          {content.content.map((block: any, index: number) => {
            if (block.type === 'paragraph') {
              return (
                <p key={index}>
                  {block.content?.map((inline: any, inlineIndex: number) => (
                    <span key={inlineIndex}>{inline.text}</span>
                  ))}
                </p>
              );
            }
            return null;
          })}
        </div>
      );
    }
    
    return <pre className="text-sm">{JSON.stringify(content, null, 2)}</pre>;
  };

  const getSEOPreview = () => {
    const title = post.meta_title || post.title;
    const description = post.meta_description || post.excerpt || 'No description available';
    const url = `https://yoursite.com/posts/${post.slug}`;
    
    return { title, description, url };
  };

  const seoPreview = getSEOPreview();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Post Preview: {post.title}</span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreviewMode(previewMode === 'desktop' ? 'mobile' : 'desktop')}
              >
                {previewMode === 'desktop' ? (
                  <Smartphone className="h-4 w-4" />
                ) : (
                  <Monitor className="h-4 w-4" />
                )}
                {previewMode === 'desktop' ? 'Mobile' : 'Desktop'}
              </Button>
              <Button variant="outline" size="sm" asChild>
                <a href={`/posts/${post.slug}`} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  View Live
                </a>
              </Button>
            </div>
          </DialogTitle>
          <DialogDescription>
            Preview how this post will appear to readers
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="content" className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="seo">SEO Preview</TabsTrigger>
            <TabsTrigger value="social">Social Media</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="mt-4">
            <div className={`mx-auto transition-all duration-300 ${
              previewMode === 'mobile' ? 'max-w-sm' : 'max-w-4xl'
            }`}>
              <Card>
                <CardHeader>
                  {post.featured_image_url && (
                    <div className="aspect-video w-full overflow-hidden rounded-lg mb-4">
                      <img
                        src={post.featured_image_url}
                        alt={post.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <CardTitle className="text-2xl">{post.title}</CardTitle>
                  {post.excerpt && (
                    <CardDescription className="text-base">
                      {post.excerpt}
                    </CardDescription>
                  )}
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    {post.author && (
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={post.author.avatar_url} />
                          <AvatarFallback>
                            {post.author.full_name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <span>{post.author.full_name}</span>
                      </div>
                    )}
                    {post.published_at && (
                      <span>{format(new Date(post.published_at), 'PPP')}</span>
                    )}
                    {post.category && (
                      <Badge variant="outline">{post.category.name}</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    {renderContent(post.content)}
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="seo" className="mt-4">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    Google Search Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-4 bg-white">
                    <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                      {seoPreview.title}
                    </div>
                    <div className="text-green-700 text-sm">
                      {seoPreview.url}
                    </div>
                    <div className="text-gray-600 text-sm mt-1">
                      {seoPreview.description}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">SEO Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span>Title Length:</span>
                      <Badge variant={seoPreview.title.length <= 60 ? "default" : "destructive"}>
                        {seoPreview.title.length}/60
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Description Length:</span>
                      <Badge variant={seoPreview.description.length <= 160 ? "default" : "destructive"}>
                        {seoPreview.description.length}/160
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Featured Image:</span>
                      <Badge variant={post.featured_image_url ? "default" : "secondary"}>
                        {post.featured_image_url ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Content Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span>Word Count:</span>
                      <Badge variant="outline">
                        {post.content ? JSON.stringify(post.content).length : 0}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Reading Time:</span>
                      <Badge variant="outline">
                        {Math.ceil((post.content ? JSON.stringify(post.content).length : 0) / 1000)} min
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="social" className="mt-4">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Facebook/LinkedIn Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg overflow-hidden bg-white max-w-lg">
                    {post.featured_image_url && (
                      <div className="aspect-video w-full">
                        <img
                          src={post.featured_image_url}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <div className="p-4">
                      <div className="text-gray-500 text-xs uppercase mb-1">
                        yoursite.com
                      </div>
                      <div className="font-semibold text-gray-900 mb-1">
                        {seoPreview.title}
                      </div>
                      <div className="text-gray-600 text-sm">
                        {seoPreview.description}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Twitter Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-xl overflow-hidden bg-white max-w-lg">
                    {post.featured_image_url && (
                      <div className="aspect-video w-full">
                        <img
                          src={post.featured_image_url}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <div className="p-4">
                      <div className="font-semibold text-gray-900 mb-1">
                        {seoPreview.title}
                      </div>
                      <div className="text-gray-600 text-sm mb-2">
                        {seoPreview.description}
                      </div>
                      <div className="text-gray-500 text-sm">
                        🔗 {seoPreview.url}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="mt-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Post Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      Status:
                    </span>
                    <Badge>{post.status}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Created:
                    </span>
                    <span className="text-sm">
                      {post.created_at && formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Updated:
                    </span>
                    <span className="text-sm">
                      {post.updated_at && formatDistanceToNow(new Date(post.updated_at), { addSuffix: true })}
                    </span>
                  </div>
                  {post.published_at && (
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Published:
                      </span>
                      <span className="text-sm">
                        {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Content Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {post.author && (
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Author:
                      </span>
                      <span className="text-sm">{post.author.full_name}</span>
                    </div>
                  )}
                  {post.category && (
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Hash className="h-4 w-4" />
                        Category:
                      </span>
                      <Badge variant="outline">{post.category.name}</Badge>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Slug:
                    </span>
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {post.slug}
                    </code>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

export function PostHoverCard({ post, children }: PostHoverCardProps) {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="space-y-3">
          {post.featured_image_url && (
            <div className="aspect-video w-full overflow-hidden rounded">
              <img
                src={post.featured_image_url}
                alt={post.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}
          <div>
            <h4 className="font-semibold">{post.title}</h4>
            {post.excerpt && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-3">
                {post.excerpt}
              </p>
            )}
          </div>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              {post.author && (
                <span>{post.author.full_name}</span>
              )}
              {post.category && (
                <Badge variant="outline" className="text-xs">
                  {post.category.name}
                </Badge>
              )}
            </div>
            <Badge variant="outline" className="text-xs">
              {post.status}
            </Badge>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
