/**
 * Example component demonstrating proper usage of database types
 * This shows how to use the generated types in React components
 */

'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/client';
import type { PostWithRelations, ContentStatus, LoadingState } from '@/types';

interface PostListProps {
  status?: ContentStatus;
  limit?: number;
}

export function TypedPostList({ status = 'published', limit = 10 }: PostListProps) {
  const [posts, setPosts] = useState<PostWithRelations[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: undefined,
  });

  useEffect(() => {
    async function fetchPosts() {
      setLoading({ isLoading: true, error: undefined });
      
      try {
        const supabase = createClient();
        
        // Type-safe database query with relations
        const { data, error } = await supabase
          .from('posts')
          .select(`
            *,
            author:profiles!posts_author_id_fkey(
              id,
              full_name,
              email,
              avatar_url
            ),
            category:categories!posts_category_id_fkey(
              id,
              name,
              slug
            ),
            tags:post_tags!inner(
              tag:tags!inner(
                id,
                name,
                slug
              )
            )
          `)
          .eq('status', status)
          .is('deleted_at', null)
          .order('created_at', { ascending: false })
          .limit(limit);

        if (error) {
          throw new Error(error.message);
        }

        // TypeScript knows the exact shape of the data
        setPosts(data as PostWithRelations[]);
        setLoading({ isLoading: false });
      } catch (error) {
        setLoading({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to fetch posts',
        });
      }
    }

    fetchPosts();
  }, [status, limit]);

  if (loading.isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading posts...</div>
      </div>
    );
  }

  if (loading.error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-destructive">Error: {loading.error}</div>
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">No posts found.</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">
        {status === 'published' ? 'Published Posts' : `${status} Posts`}
      </h2>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {posts.map((post) => (
          <PostCard key={post.id} post={post} />
        ))}
      </div>
    </div>
  );
}

interface PostCardProps {
  post: PostWithRelations;
}

function PostCard({ post }: PostCardProps) {
  // TypeScript provides full intellisense for all properties
  const {
    id,
    title,
    slug,
    excerpt,
    featured_image_url,
    published_at,
    author,
    category,
    tags,
  } = post;

  return (
    <article className="rounded-lg border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
      {featured_image_url && (
        <div className="mb-4 aspect-video overflow-hidden rounded-md">
          <img
            src={featured_image_url}
            alt={title}
            className="h-full w-full object-cover"
          />
        </div>
      )}
      
      <div className="space-y-3">
        <div className="space-y-1">
          <h3 className="font-semibold leading-tight">
            <a
              href={`/posts/${slug}`}
              className="hover:text-primary transition-colors"
            >
              {title}
            </a>
          </h3>
          
          {excerpt && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {excerpt}
            </p>
          )}
        </div>

        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {author && (
            <span>By {author.full_name || author.email}</span>
          )}
          
          {published_at && (
            <>
              <span>•</span>
              <time dateTime={published_at.toString()}>
                {new Date(published_at).toLocaleDateString()}
              </time>
            </>
          )}
        </div>

        {category && (
          <div className="flex items-center gap-1">
            <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
              {category.name}
            </span>
          </div>
        )}

        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {tags.map((tagRelation) => {
              // Handle the nested tag structure from the join
              const tag = 'tag' in tagRelation ? tagRelation.tag : tagRelation;
              return (
                <span
                  key={tag.id}
                  className="inline-flex items-center rounded-full bg-secondary px-2 py-1 text-xs text-secondary-foreground"
                >
                  {tag.name}
                </span>
              );
            })}
          </div>
        )}
      </div>
    </article>
  );
}

// Example of a form component with proper typing
interface CreatePostFormData {
  title: string;
  slug: string;
  excerpt?: string;
  content?: Record<string, any>;
  category_id?: string;
  status: ContentStatus;
}

export function CreatePostForm() {
  const [formData, setFormData] = useState<CreatePostFormData>({
    title: '',
    slug: '',
    excerpt: '',
    content: undefined,
    category_id: undefined,
    status: 'draft',
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const supabase = createClient();
      
      // Get current user for author_id
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Type-safe insert operation
      const { data, error } = await supabase
        .from('posts')
        .insert({
          ...formData,
          author_id: user.id,
          // created_at and updated_at are handled by the database
        })
        .select()
        .single();

      if (error) throw new Error(error.message);

      console.log('Post created:', data);
      // Handle success (redirect, show toast, etc.)
    } catch (error) {
      console.error('Error creating post:', error);
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="title" className="block text-sm font-medium">
          Title
        </label>
        <input
          id="title"
          type="text"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          className="mt-1 block w-full rounded-md border border-input px-3 py-2"
          required
        />
      </div>

      <div>
        <label htmlFor="slug" className="block text-sm font-medium">
          Slug
        </label>
        <input
          id="slug"
          type="text"
          value={formData.slug}
          onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
          className="mt-1 block w-full rounded-md border border-input px-3 py-2"
          required
        />
      </div>

      <div>
        <label htmlFor="status" className="block text-sm font-medium">
          Status
        </label>
        <select
          id="status"
          value={formData.status}
          onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as ContentStatus }))}
          className="mt-1 block w-full rounded-md border border-input px-3 py-2"
        >
          <option value="draft">Draft</option>
          <option value="pending_review">Pending Review</option>
          <option value="published">Published</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      <button
        type="submit"
        disabled={loading}
        className="rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
      >
        {loading ? 'Creating...' : 'Create Post'}
      </button>
    </form>
  );
}
