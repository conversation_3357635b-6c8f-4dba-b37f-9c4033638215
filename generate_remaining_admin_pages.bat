@echo off
REM Windows Batch script to generate admin Next.js App Router pages within an (admin) group.

REM Base directory for the app
SET "APP_ROOT_DIR=src\app"

REM Admin group directory
SET "ADMIN_GROUP_DIR=%APP_ROOT_DIR%\admin"

REM Ensure the base app directory exists
IF NOT EXIST "%APP_ROOT_DIR%" (
    ECHO Base app directory %APP_ROOT_DIR% does not exist. Please create it first or run this script from your project root.
    GOTO :EOF
)

REM Create the admin group directory if it doesn't exist
IF NOT EXIST "%ADMIN_GROUP_DIR%" (
    mkdir "%ADMIN_GROUP_DIR%"
    ECHO Created admin group directory: %ADMIN_GROUP_DIR%
    REM Create a placeholder layout for the admin group if it's newly created
    ECHO // %ADMIN_GROUP_DIR%\layout.tsx - Shared layout for all admin routes. > "%ADMIN_GROUP_DIR%\layout.tsx"
    ECHO // Include Sidebar, Navbar, and authentication protection here. >> "%ADMIN_GROUP_DIR%\layout.tsx"
    ECHO Placeholder admin layout.tsx created. Please implement it.
) ELSE (
    ECHO Admin group directory %ADMIN_GROUP_DIR% already exists.
    IF NOT EXIST "%ADMIN_GROUP_DIR%\layout.tsx" (
        ECHO // %ADMIN_GROUP_DIR%\layout.tsx - Shared layout for all admin routes. > "%ADMIN_GROUP_DIR%\layout.tsx"
        ECHO // Include Sidebar, Navbar, and authentication protection here. >> "%ADMIN_GROUP_DIR%\layout.tsx"
        ECHO Placeholder admin layout.tsx created as it was missing. Please implement it.
    )
)


ECHO.
ECHO Generating admin page structure in "%ADMIN_GROUP_DIR%"...
ECHO Ensure your dashboard is moved to/located at %ADMIN_GROUP_DIR%\dashboard\page.tsx
ECHO to share the %ADMIN_GROUP_DIR%\layout.tsx.
ECHO.

REM --- Posts Section ---
SET "POSTS_DIR=%ADMIN_GROUP_DIR%\posts"
IF NOT EXIST "%POSTS_DIR%" mkdir "%POSTS_DIR%"
ECHO // %POSTS_DIR%\page.tsx - Lists all blog posts. > "%POSTS_DIR%\page.tsx"
IF NOT EXIST "%POSTS_DIR%\new" mkdir "%POSTS_DIR%\new"
ECHO // %POSTS_DIR%\new\page.tsx - Form to create a new blog post. > "%POSTS_DIR%\new\page.tsx"
IF NOT EXIST "%POSTS_DIR%\[id]" mkdir "%POSTS_DIR%\[id]"
IF NOT EXIST "%POSTS_DIR%\[id]\edit" mkdir "%POSTS_DIR%\[id]\edit"
ECHO // %POSTS_DIR%\[id]\edit\page.tsx - Form to edit an existing blog post. > "%POSTS_DIR%\[id]\edit\page.tsx"
ECHO Generated: Posts section

REM --- Pages Section ---
SET "PAGES_DIR=%ADMIN_GROUP_DIR%\pages"
IF NOT EXIST "%PAGES_DIR%" mkdir "%PAGES_DIR%"
ECHO // %PAGES_DIR%\page.tsx - Lists all static website pages. > "%PAGES_DIR%\page.tsx"
IF NOT EXIST "%PAGES_DIR%\new" mkdir "%PAGES_DIR%\new"
ECHO // %PAGES_DIR%\new\page.tsx - Form to create a new static page. > "%PAGES_DIR%\new\page.tsx"
IF NOT EXIST "%PAGES_DIR%\[slug]" mkdir "%PAGES_DIR%\[slug]"
IF NOT EXIST "%PAGES_DIR%\[slug]\edit" mkdir "%PAGES_DIR%\[slug]\edit"
ECHO // %PAGES_DIR%\[slug]\edit\page.tsx - Form to edit an existing static page. > "%PAGES_DIR%\[slug]\edit\page.tsx"
ECHO Generated: Pages section

REM --- Biographies Section ---
SET "BIOS_DIR=%ADMIN_GROUP_DIR%\biographies"
IF NOT EXIST "%BIOS_DIR%" mkdir "%BIOS_DIR%"
ECHO // %BIOS_DIR%\page.tsx - Lists all biographies. > "%BIOS_DIR%\page.tsx"
IF NOT EXIST "%BIOS_DIR%\new" mkdir "%BIOS_DIR%\new"
ECHO // %BIOS_DIR%\new\page.tsx - Form to create a new biography. > "%BIOS_DIR%\new\page.tsx"
IF NOT EXIST "%BIOS_DIR%\[id]" mkdir "%BIOS_DIR%\[id]"
IF NOT EXIST "%BIOS_DIR%\[id]\edit" mkdir "%BIOS_DIR%\[id]\edit"
ECHO // %BIOS_DIR%\[id]\edit\page.tsx - Form to edit an existing biography. > "%BIOS_DIR%\[id]\edit\page.tsx"
ECHO Generated: Biographies section

REM --- Categories Page ---
SET "CATEGORIES_DIR=%ADMIN_GROUP_DIR%\categories"
IF NOT EXIST "%CATEGORIES_DIR%" mkdir "%CATEGORIES_DIR%"
ECHO // %CATEGORIES_DIR%\page.tsx - Page to manage content categories. > "%CATEGORIES_DIR%\page.tsx"
ECHO Generated: Categories page

REM --- Tags Page ---
SET "TAGS_DIR=%ADMIN_GROUP_DIR%\tags"
IF NOT EXIST "%TAGS_DIR%" mkdir "%TAGS_DIR%"
ECHO // %TAGS_DIR%\page.tsx - Page to manage content tags. > "%TAGS_DIR%\page.tsx"
ECHO Generated: Tags page

REM --- Users Section (Admin Only) ---
SET "USERS_DIR=%ADMIN_GROUP_DIR%\users"
IF NOT EXIST "%USERS_DIR%" mkdir "%USERS_DIR%"
ECHO // %USERS_DIR%\page.tsx - Lists users, invite new users. (Admin Only) > "%USERS_DIR%\page.tsx"
IF NOT EXIST "%USERS_DIR%\[id]" mkdir "%USERS_DIR%\[id]"
IF NOT EXIST "%USERS_DIR%\[id]\edit" mkdir "%USERS_DIR%\[id]\edit"
ECHO // %USERS_DIR%\[id]\edit\page.tsx - Page to edit user details. (Admin Only) > "%USERS_DIR%\[id]\edit\page.tsx"
ECHO Generated: Users section (Admin Only)

REM --- Settings Page (Admin Only) ---
SET "SETTINGS_DIR=%ADMIN_GROUP_DIR%\settings"
IF NOT EXIST "%SETTINGS_DIR%" mkdir "%SETTINGS_DIR%"
ECHO // %SETTINGS_DIR%\page.tsx - Manage organization-wide settings. (Admin Only) > "%SETTINGS_DIR%\page.tsx"
ECHO Generated: Settings page (Admin Only)

REM --- Audit Log Page (Admin Only) ---
SET "AUDIT_LOG_DIR=%ADMIN_GROUP_DIR%\audit-log"
IF NOT EXIST "%AUDIT_LOG_DIR%" mkdir "%AUDIT_LOG_DIR%"
ECHO // %AUDIT_LOG_DIR%\page.tsx - View audit log. (Admin Only) > "%AUDIT_LOG_DIR%\page.tsx"
ECHO Generated: Audit Log page (Admin Only)

REM --- Profile Page (Current User) ---
SET "PROFILE_DIR=%ADMIN_GROUP_DIR%\profile"
IF NOT EXIST "%PROFILE_DIR%" mkdir "%PROFILE_DIR%"
ECHO // %PROFILE_DIR%\page.tsx - Edit current user's profile. > "%PROFILE_DIR%\page.tsx"
ECHO Generated: Profile page

ECHO.
ECHO Admin page structure with (admin) group generated in "%ADMIN_GROUP_DIR%".
ECHO Files contain comments explaining their purpose.
ECHO Implement or verify your "%ADMIN_GROUP_DIR%\layout.tsx" to provide shared UI and protection.

:EOF