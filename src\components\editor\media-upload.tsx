'use client';

import { useState, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Upload, Image as ImageIcon, File, X, Check, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MediaFile {
  id: string;
  file: File;
  preview?: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

interface MediaUploadProps {
  onUpload?: (files: MediaFile[]) => void;
  onFileSelect?: (url: string) => void;
  accept?: string;
  maxFiles?: number;
  maxSize?: number;
  className?: string;
  showPreview?: boolean;
}

export function MediaUpload({
  onUpload,
  onFileSelect,
  accept = 'image/*,.pdf,.txt,.md',
  maxFiles = 10,
  maxSize = 5 * 1024 * 1024, // 5MB
  className,
  showPreview = true,
}: MediaUploadProps) {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = Array.from(event.target.files || []);

      // Process selected files
      const newFiles: MediaFile[] = selectedFiles.map((file) => ({
        id: Math.random().toString(36).substr(2, 9),
        file,
        preview: file.type.startsWith('image/')
          ? URL.createObjectURL(file)
          : undefined,
        progress: 0,
        status: 'uploading',
      }));

      setFiles((prev) => [...prev, ...newFiles]);

      // Simulate upload process
      newFiles.forEach((mediaFile) => {
        simulateUpload(mediaFile);
      });

      onUpload?.(newFiles);

      // Reset input
      if (event.target) {
        event.target.value = '';
      }
    },
    [onUpload]
  );

  const simulateUpload = (mediaFile: MediaFile) => {
    const interval = setInterval(() => {
      setFiles((prev) =>
        prev.map((f) => {
          if (f.id === mediaFile.id) {
            const newProgress = Math.min(f.progress + 10, 100);
            const isComplete = newProgress === 100;

            return {
              ...f,
              progress: newProgress,
              status: isComplete ? 'completed' : 'uploading',
              url: isComplete ? URL.createObjectURL(f.file) : undefined,
            };
          }
          return f;
        })
      );

      if (mediaFile.progress >= 100) {
        clearInterval(interval);
      }
    }, 200);
  };

  const removeFile = (id: string) => {
    setFiles((prev) => {
      const fileToRemove = prev.find((f) => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter((f) => f.id !== id);
    });
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <ImageIcon className="h-8 w-8" />;
    }
    return <File className="h-8 w-8" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Media Upload
          </CardTitle>
          <CardDescription>
            Upload images, documents, and other media files
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed rounded-lg p-8 text-center">
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Upload Media Files</p>
            <p className="text-sm text-muted-foreground mb-4">
              Select files to upload
            </p>
            <div className="flex flex-wrap justify-center gap-2 text-xs text-muted-foreground mb-4">
              <Badge variant="outline">Max {maxFiles} files</Badge>
              <Badge variant="outline">
                Max {formatFileSize(maxSize)} each
              </Badge>
              <Badge variant="outline">Images, PDFs, Text</Badge>
            </div>
            <Input
              ref={fileInputRef}
              type="file"
              accept={accept}
              multiple
              onChange={handleFileSelect}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="w-full max-w-xs"
            >
              <Upload className="h-4 w-4 mr-2" />
              Choose Files
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Uploaded Files</CardTitle>
            <CardDescription>
              {files.length} file{files.length !== 1 ? 's' : ''} uploaded
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {files.map((mediaFile) => (
                <div
                  key={mediaFile.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  {/* File Icon/Preview */}
                  <div className="flex-shrink-0">
                    {mediaFile.preview && showPreview ? (
                      <img
                        src={mediaFile.preview}
                        alt={mediaFile.file.name}
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      <div className="h-12 w-12 flex items-center justify-center bg-muted rounded">
                        {getFileIcon(mediaFile.file)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">
                      {mediaFile.file.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(mediaFile.file.size)}
                    </p>

                    {/* Progress Bar */}
                    {mediaFile.status === 'uploading' && (
                      <div className="mt-2">
                        <Progress value={mediaFile.progress} className="h-2" />
                        <p className="text-xs text-muted-foreground mt-1">
                          {mediaFile.progress}% uploaded
                        </p>
                      </div>
                    )}

                    {/* Error Message */}
                    {mediaFile.status === 'error' && mediaFile.error && (
                      <div className="mt-2 text-xs text-destructive">
                        {mediaFile.error}
                      </div>
                    )}
                  </div>

                  {/* Status & Actions */}
                  <div className="flex items-center gap-2">
                    {mediaFile.status === 'completed' && (
                      <>
                        <Badge variant="default" className="text-xs">
                          <Check className="h-3 w-3 mr-1" />
                          Uploaded
                        </Badge>
                        {onFileSelect && mediaFile.url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onFileSelect(mediaFile.url!)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Use
                          </Button>
                        )}
                      </>
                    )}

                    {mediaFile.status === 'uploading' && (
                      <Badge variant="secondary" className="text-xs">
                        Uploading...
                      </Badge>
                    )}

                    {mediaFile.status === 'error' && (
                      <Badge variant="destructive" className="text-xs">
                        Error
                      </Badge>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(mediaFile.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
