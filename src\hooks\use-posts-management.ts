'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getPosts, 
  getCategories, 
  getAuthors, 
  getTags,
  updatePost,
  deletePost,
  duplicatePost,
  bulkUpdatePosts,
  bulkDeletePosts
} from '@/lib/database-helpers';
import type { 
  PostWithRelations, 
  Category, 
  Profile, 
  Tag, 
  QueryParams, 
  ContentStatus,
  PaginatedResponse 
} from '@/types';

interface PostsManagementState {
  posts: PostWithRelations[];
  categories: Category[];
  authors: Profile[];
  tags: Tag[];
  pagination: PaginatedResponse<PostWithRelations>['pagination'];
  loading: {
    posts: boolean;
    categories: boolean;
    authors: boolean;
    tags: boolean;
    actions: boolean;
  };
  errors: {
    posts?: string;
    categories?: string;
    authors?: string;
    tags?: string;
    actions?: string;
  };
}

export function usePostsManagement() {
  const [state, setState] = useState<PostsManagementState>({
    posts: [],
    categories: [],
    authors: [],
    tags: [],
    pagination: undefined,
    loading: {
      posts: true,
      categories: true,
      authors: true,
      tags: true,
      actions: false,
    },
    errors: {},
  });

  const [filters, setFilters] = useState<QueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // Fetch posts with current filters
  const fetchPosts = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, posts: true },
      errors: { ...prev.errors, posts: undefined }
    }));

    try {
      const result = await getPosts(filters);
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          posts: result.data!,
          pagination: result.pagination,
          loading: { ...prev.loading, posts: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, posts: false },
          errors: { ...prev.errors, posts: result.error || 'Failed to fetch posts' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, posts: false },
        errors: { 
          ...prev.errors, 
          posts: error instanceof Error ? error.message : 'Failed to fetch posts' 
        },
      }));
    }
  }, [filters]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, categories: true },
      errors: { ...prev.errors, categories: undefined }
    }));

    try {
      const result = await getCategories();
      
      if (result.data) {
        setState(prev => ({
          ...prev,
          categories: result.data!,
          loading: { ...prev.loading, categories: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, categories: false },
          errors: { ...prev.errors, categories: result.error || 'Failed to fetch categories' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, categories: false },
        errors: { 
          ...prev.errors, 
          categories: error instanceof Error ? error.message : 'Failed to fetch categories' 
        },
      }));
    }
  }, []);

  // Fetch authors
  const fetchAuthors = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, authors: true },
      errors: { ...prev.errors, authors: undefined }
    }));

    try {
      const result = await getAuthors();
      
      if (result.data) {
        setState(prev => ({
          ...prev,
          authors: result.data!,
          loading: { ...prev.loading, authors: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, authors: false },
          errors: { ...prev.errors, authors: result.error || 'Failed to fetch authors' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, authors: false },
        errors: { 
          ...prev.errors, 
          authors: error instanceof Error ? error.message : 'Failed to fetch authors' 
        },
      }));
    }
  }, []);

  // Fetch tags
  const fetchTags = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, tags: true },
      errors: { ...prev.errors, tags: undefined }
    }));

    try {
      const result = await getTags();
      
      if (result.data) {
        setState(prev => ({
          ...prev,
          tags: result.data!,
          loading: { ...prev.loading, tags: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, tags: false },
          errors: { ...prev.errors, tags: result.error || 'Failed to fetch tags' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, tags: false },
        errors: { 
          ...prev.errors, 
          tags: error instanceof Error ? error.message : 'Failed to fetch tags' 
        },
      }));
    }
  }, []);

  // Update filters and refetch posts
  const updateFilters = useCallback((newFilters: Partial<QueryParams>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 10,
      sortBy: 'created_at',
      sortOrder: 'desc',
    });
  }, []);

  // Single post actions
  const handleUpdatePost = useCallback(async (id: string, updates: any) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await updatePost(id, updates);
      
      if (result.data) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to update post' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update post';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  const handleDeletePost = useCallback(async (id: string) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await deletePost(id);
      
      if (result.success) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to delete post' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete post';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  const handleDuplicatePost = useCallback(async (id: string) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await duplicatePost(id);
      
      if (result.data) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true, data: result.data };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to duplicate post' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to duplicate post';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  // Bulk actions
  const handleBulkStatusChange = useCallback(async (postIds: string[], newStatus: ContentStatus) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await bulkUpdatePosts(postIds, { status: newStatus });
      
      if (result.success) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to update posts' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update posts';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  const handleBulkCategoryChange = useCallback(async (postIds: string[], categoryId: string) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await bulkUpdatePosts(postIds, { category_id: categoryId });
      
      if (result.success) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to update posts' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update posts';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  const handleBulkDelete = useCallback(async (postIds: string[]) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await bulkDeletePosts(postIds);
      
      if (result.success) {
        await fetchPosts(); // Refresh posts list
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
        }));
        return { success: true };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error || 'Failed to delete posts' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete posts';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage },
      }));
      return { success: false, error: errorMessage };
    }
  }, [fetchPosts]);

  // Initial data fetch
  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  useEffect(() => {
    fetchCategories();
    fetchAuthors();
    fetchTags();
  }, [fetchCategories, fetchAuthors, fetchTags]);

  return {
    // State
    posts: state.posts,
    categories: state.categories,
    authors: state.authors,
    tags: state.tags,
    pagination: state.pagination,
    loading: state.loading,
    errors: state.errors,
    filters,

    // Actions
    updateFilters,
    resetFilters,
    refresh: fetchPosts,
    
    // Post actions
    updatePost: handleUpdatePost,
    deletePost: handleDeletePost,
    duplicatePost: handleDuplicatePost,
    
    // Bulk actions
    bulkStatusChange: handleBulkStatusChange,
    bulkCategoryChange: handleBulkCategoryChange,
    bulkDelete: handleBulkDelete,
  };
}
