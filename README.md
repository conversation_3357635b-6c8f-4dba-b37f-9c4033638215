# Organization Hub - Admin & Governance Platform

## 🚀 Overview

This project is a Next.js web application serving as a central hub for running and governing an organization. It provides an administrative interface for managing website content (blog posts, static pages, biographies), user roles and permissions, organization settings, and newsletter subscriptions. The data managed through this hub is primarily consumed by a separate, public-facing frontend website that displays information to the general public.

The Admin Hub itself is an internal tool, accessible only to authenticated users with specific roles (Admin, Publisher, Editor, Member) and an invite-only registration system.

## ✨ Core Features

- **Role-Based Access Control (RBAC):**
  - **Admin:** Top-level permissions; manages users, settings, all content, audit logs.
  - **Publisher:** Can create, edit, and publish/unpublish all content types; manages categories and tags.
  - **Editor:** Can create and edit content, submitting it for review; can manage their own drafts.
  - **Member:** Lowest authenticated level; potential for content proposal or specific read access.
- **Content Management:**
  - **Posts:** CRUD operations for blog articles, including rich text editing, featured images, categorization, and tagging.
  - **Pages:** CRUD operations for static website pages (e.g., "About Us," "Contact").
  - **Biographies:** Manage profiles of organization members (e.g., team, board) for public display.
- **User Management (Admin-only):**
  - Invite-only system for new user registration.
  - List, edit (roles), and manage user statuses.
- **Organization Settings (Admin-only):**
  - Manage global settings like organization name, logo, public site URL, and API keys for third-party services (e.g., newsletter platforms).
- **Newsletter Subscription Management:**
  - View list of subscribers.
  - The public frontend handles sign-ups, which are processed via secure Supabase Edge Functions and stored here.
  - Supports a double opt-in mechanism.
- **Audit Logging (Admin-only):**
  - Tracks significant actions performed within the system (creates, updates, deletes, user actions).
- **Soft Deletes:** Content and user data can be soft-deleted, allowing for recovery.
- **Invite-Only Registration:** New users can only join the Admin Hub via an email invitation sent by an Administrator.

## 🛠️ Tech Stack

- **Frontend (Admin Hub):**
  - **Framework:** Next.js (App Router)
  - **Language:** TypeScript
  - **Styling:** Tailwind CSS
  - **Component Library:** Shadcn/UI
  - **State Management:** React Query/TanStack Query (for server state)
  - **Forms:** React Hook Form + Zod (for validation)
  - **Rich Text Editor:** TipTap
- **Backend & Database:**
  - **Platform:** Supabase
    - **Database:** PostgreSQL
    - **Authentication:** Supabase Auth (for Admin Hub users)
    - **Storage:** Supabase Storage (for images, logos, etc.)
    - **Edge Functions:** For server-side logic like newsletter sign-ups, email sending.
- **Deployment:**
  - **Admin Hub (Next.js):** Vercel
  - **Public Frontend Website:** (Separate project, can be any static/dynamic site generator like Next.js, Astro, etc., deployed to Vercel, Netlify, etc.)

## DATABASE_STRUCTURE

The database schema is designed in PostgreSQL within Supabase. Key tables include:

- `profiles`: Extends `auth.users` to store application-specific user data, including their `role`.
- `invitations`: Manages the invite-only registration process.
- `posts`: Stores blog articles.
- `pages`: Stores static website content.
- `biographies`: Stores profiles of organization members.
- `categories`, `tags`: For content organization.
- `post_tags`: Junction table for posts and tags.
- `organization_settings`: Key-value store for global settings.
- `newsletter_subscribers`: Stores email addresses and subscription status for newsletters.
- `audit_log`: Records important actions within the system.
- `post_versions` (and similar for pages/biographies): For content versioning (optional implementation via triggers).

**Key Concepts:**

- **Soft Deletes:** Most content tables use a `deleted_at` timestamp for soft deletion.
- **Row Level Security (RLS):** Extensively used to enforce permissions directly at the database level based on user roles.
- **Triggers:** Used for:
  - Automatically updating `updated_at` timestamps.
  - Processing new user sign-ups from invitations (`handle_new_user_with_invitation` on `auth.users`).
  - Populating the `audit_log` table (`audit_trigger_function` on various tables).

For detailed schema definitions and RLS policies, refer to the SQL scripts in the `database/` directory.

## 🚀 Getting Started

### Prerequisites

- Node.js (version specified in `.nvmrc` or latest LTS)
- npm, yarn, or pnpm
- Supabase Account & Project
- Supabase CLI

### Setup

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Install dependencies:**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up Supabase Project:**

   - Create a new project on [Supabase](https://supabase.com).
   - In your Supabase project dashboard, go to "SQL Editor" and run the database schema setup script (e.g., `database_setup.sql` - ensure you have this script containing all `CREATE TABLE`, `CREATE FUNCTION`, RLS policies, etc.).
   - Enable any required database extensions (e.g., `uuid-ossp`, `pgcrypto`) if not enabled by default.
   - Configure Supabase Auth settings (e.g., enable email provider, disable public sign-ups, set up email templates).

4. **Configure Environment Variables:**

   - Copy the example environment file:

     ```bash
     cp .env.local.example .env.local
     ```

   - Fill in the required Supabase credentials in `.env.local`:

     ```env
     NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
     SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key # Used for admin tasks, Edge Functions. Keep this secret!

     # Optional: For sending emails (e.g., invitations)
     # RESEND_API_KEY=your-resend-api-key (or other email provider)
     ```

   - You can find the URL and ANON_KEY in your Supabase project's "API Settings". The SERVICE_ROLE_KEY is also there (treat it like a password).

5. **Link Supabase Project & Generate Types:**

   ```bash
   npx supabase login
   npx supabase link --project-ref <your-project-ref-from-dashboard-url>
   # (Enter database password when prompted)

   npm run db:types # Or the equivalent command to generate database types
   # (Assumes: "db:types": "supabase gen types typescript --project-id <your-project-ref> --schema public > src/lib/types/database.types.ts" in package.json)
   ```

6. **Initialize Shadcn/UI (if not already done by a template):**

   ```bash
   npx shadcn-ui@latest init
   ```

   Follow the prompts. Then add desired components:

   ```bash
   npx shadcn-ui@latest add button input label ... # (as needed)
   ```

7. **Seed Initial Data (Optional but Recommended for Development):**
   - The first user (Admin) might need to be created manually or through a seed script to send out the first invitations.
   - You can use the Supabase dashboard's "Table Editor" to import CSV data or run SQL seed scripts.

### Running the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```
