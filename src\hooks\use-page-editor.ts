'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getPage, 
  createPage, 
  updatePage,
  getAuthors
} from '@/lib/database-helpers';
import type { 
  Page,
  PageWithRelations, 
  InsertPage,
  Profile,
  ContentStatus
} from '@/types';

interface PageFormData {
  title: string;
  slug: string;
  content: Record<string, any>;
  author_id?: string;
  status: ContentStatus;
  published_at?: Date;
  meta_title?: string;
  meta_description?: string;
  template_id?: string;
  parent_id?: string;
  sort_order?: number;
}

interface PageEditorState {
  formData: PageFormData;
  originalPage?: PageWithRelations;
  authors: Profile[];
  loading: {
    page: boolean;
    authors: boolean;
    saving: boolean;
  };
  errors: {
    page?: string;
    authors?: string;
    saving?: string;
    validation?: Record<string, string>;
  };
  isDirty: boolean;
  lastSaved?: Date;
}

interface PageEditorActions {
  updateFormData: (updates: Partial<PageFormData>) => void;
  savePage: () => Promise<{ success: boolean; data?: Page; error?: string }>;
  loadPage: (id: string) => Promise<void>;
  loadAuthors: <AUTHORS>
  validateForm: () => boolean;
  resetForm: () => void;
}

const initialFormData: PageFormData = {
  title: '',
  slug: '',
  content: {},
  status: 'draft',
  meta_title: '',
  meta_description: '',
};

export function usePageEditor(pageId?: string): PageEditorState & PageEditorActions {
  const [state, setState] = useState<PageEditorState>({
    formData: initialFormData,
    authors: [],
    loading: {
      page: false,
      authors: false,
      saving: false,
    },
    errors: {},
    isDirty: false,
  });

  const updateFormData = useCallback((updates: Partial<PageFormData>) => {
    setState(prev => ({
      ...prev,
      formData: { ...prev.formData, ...updates },
      isDirty: true,
      errors: { ...prev.errors, validation: undefined },
    }));
  }, []);

  const validateForm = useCallback((): boolean => {
    const { title, slug } = state.formData;
    const errors: Record<string, string> = {};

    if (!title.trim()) {
      errors.title = 'Title is required';
    }

    if (!slug.trim()) {
      errors.slug = 'URL slug is required';
    } else if (!/^[a-z0-9-]+$/.test(slug)) {
      errors.slug = 'URL slug can only contain lowercase letters, numbers, and hyphens';
    }

    setState(prev => ({
      ...prev,
      errors: { ...prev.errors, validation: Object.keys(errors).length > 0 ? errors : undefined },
    }));

    return Object.keys(errors).length === 0;
  }, [state.formData]);

  const savePage = useCallback(async () => {
    if (!validateForm()) {
      return { success: false, error: 'Please fix validation errors' };
    }

    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, saving: true },
      errors: { ...prev.errors, saving: undefined },
    }));

    try {
      const pageData: InsertPage = {
        title: state.formData.title,
        slug: state.formData.slug,
        content: state.formData.content,
        author_id: state.formData.author_id,
        status: state.formData.status,
        published_at: state.formData.published_at?.toISOString(),
        meta_title: state.formData.meta_title,
        meta_description: state.formData.meta_description,
      };

      let result;
      if (pageId) {
        // Update existing page
        result = await updatePage(pageId, pageData);
      } else {
        // Create new page
        result = await createPage(pageData);
      }

      if (result.data) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, saving: false },
          isDirty: false,
          lastSaved: new Date(),
        }));
        return { success: true, data: result.data };
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, saving: false },
          errors: { ...prev.errors, saving: result.error || 'Failed to save page' },
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, saving: false },
        errors: { ...prev.errors, saving: 'An unexpected error occurred' },
      }));
      return { success: false, error: 'An unexpected error occurred' };
    }
  }, [pageId, state.formData, validateForm]);

  const loadPage = useCallback(async (id: string) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, page: true },
      errors: { ...prev.errors, page: undefined },
    }));

    try {
      const result = await getPage(id);
      
      if (result.data) {
        const page = result.data;
        setState(prev => ({
          ...prev,
          originalPage: page,
          formData: {
            title: page.title,
            slug: page.slug,
            content: page.content || {},
            author_id: page.author_id,
            status: page.status,
            published_at: page.published_at ? new Date(page.published_at) : undefined,
            meta_title: page.meta_title || '',
            meta_description: page.meta_description || '',
          },
          loading: { ...prev.loading, page: false },
          isDirty: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, page: false },
          errors: { ...prev.errors, page: result.error || 'Failed to load page' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, page: false },
        errors: { ...prev.errors, page: 'An unexpected error occurred' },
      }));
    }
  }, []);

  const loadAuthors = useCallback(async () => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, authors: true },
      errors: { ...prev.errors, authors: undefined },
    }));

    try {
      const response = await getAuthors();
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          authors: response.data!,
          loading: { ...prev.loading, authors: false },
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, authors: false },
          errors: { ...prev.errors, authors: response.error || 'Failed to load authors' },
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, authors: false },
        errors: { ...prev.errors, authors: 'An unexpected error occurred' },
      }));
    }
  }, []);

  const resetForm = useCallback(() => {
    setState(prev => ({
      ...prev,
      formData: initialFormData,
      isDirty: false,
      errors: { ...prev.errors, validation: undefined },
    }));
  }, []);

  // Auto-generate slug from title
  useEffect(() => {
    if (state.formData.title && !pageId) {
      const slug = state.formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      
      if (slug !== state.formData.slug) {
        updateFormData({ slug });
      }
    }
  }, [state.formData.title, state.formData.slug, pageId, updateFormData]);

  // Load initial data
  useEffect(() => {
    loadAuthors();
    if (pageId) {
      loadPage(pageId);
    }
  }, [pageId, loadPage, loadAuthors]);

  return {
    ...state,
    updateFormData,
    savePage,
    loadPage,
    loadAuthors,
    validateForm,
    resetForm,
  };
}
