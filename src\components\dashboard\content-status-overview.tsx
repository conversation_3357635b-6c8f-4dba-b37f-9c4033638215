'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Archive, 
  AlertTriangle,
  TrendingUp,
  Users,
  UserCheck,
  UserX
} from 'lucide-react';
import type { ContentStatusBreakdown } from '@/lib/dashboard-helpers';

interface ContentStatusOverviewProps {
  statusBreakdown: ContentStatusBreakdown;
  systemHealth?: {
    status: 'healthy' | 'warning' | 'error';
    recentFailedLogins: number;
    pendingInvitations: number;
    lastChecked: Date;
  };
  isLoading?: boolean;
}

export function ContentStatusOverview({ 
  statusBreakdown, 
  systemHealth,
  isLoading 
}: ContentStatusOverviewProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Status Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            <div className="h-8 w-full bg-muted rounded"></div>
            <div className="h-64 w-full bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const total = Object.values(statusBreakdown).reduce((sum, count) => sum + count, 0);
  
  const statusData = [
    {
      name: 'Published',
      value: statusBreakdown.published,
      color: '#22c55e',
      icon: CheckCircle,
      description: 'Live content'
    },
    {
      name: 'Draft',
      value: statusBreakdown.draft,
      color: '#f59e0b',
      icon: Clock,
      description: 'Work in progress'
    },
    {
      name: 'Pending Review',
      value: statusBreakdown.pending_review,
      color: '#3b82f6',
      icon: AlertTriangle,
      description: 'Awaiting approval'
    },
    {
      name: 'Rejected',
      value: statusBreakdown.rejected,
      color: '#ef4444',
      icon: XCircle,
      description: 'Needs revision'
    },
    {
      name: 'Archived',
      value: statusBreakdown.archived,
      color: '#6b7280',
      icon: Archive,
      description: 'No longer active'
    }
  ];

  const chartData = statusData.filter(item => item.value > 0);

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'error':
        return XCircle;
      default:
        return AlertTriangle;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart className="h-5 w-5" />
          Content Status Overview
        </CardTitle>
        <CardDescription>
          Content workflow status and system health indicators
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="status" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="status">Content Status</TabsTrigger>
            <TabsTrigger value="workflow">Workflow</TabsTrigger>
            <TabsTrigger value="health">System Health</TabsTrigger>
          </TabsList>
          
          <TabsContent value="status" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Status Breakdown Chart */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Content Distribution</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Status Legend and Details */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Status Breakdown</h4>
                <div className="space-y-3">
                  {statusData.map((status) => {
                    const Icon = status.icon;
                    const percentage = total > 0 ? (status.value / total) * 100 : 0;
                    
                    return (
                      <div key={status.name} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" style={{ color: status.color }} />
                            <span className="text-sm font-medium">{status.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">{status.value}</div>
                            <div className="text-xs text-muted-foreground">
                              {percentage.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                        <Progress 
                          value={percentage} 
                          className="h-2"
                          style={{ 
                            '--progress-background': status.color 
                          } as React.CSSProperties}
                        />
                        <p className="text-xs text-muted-foreground">
                          {status.description}
                        </p>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="workflow" className="space-y-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Content Workflow Status</h4>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Count</TableHead>
                    <TableHead>Percentage</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statusData.map((status) => {
                    const percentage = total > 0 ? (status.value / total) * 100 : 0;
                    const Icon = status.icon;
                    
                    return (
                      <TableRow key={status.name}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" style={{ color: status.color }} />
                            {status.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{status.value}</Badge>
                        </TableCell>
                        <TableCell>{percentage.toFixed(1)}%</TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm" asChild>
                            <a href={`/posts?status=${status.name.toLowerCase().replace(' ', '_')}`}>
                              View
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
          
          <TabsContent value="health" className="space-y-4">
            {systemHealth && (
              <div className="space-y-4">
                <h4 className="text-sm font-medium">System Health Indicators</h4>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base flex items-center gap-2">
                        {(() => {
                          const HealthIcon = getHealthIcon(systemHealth.status);
                          return <HealthIcon className="h-4 w-4" />;
                        })()}
                        Overall Status
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Badge 
                        className={getHealthStatusColor(systemHealth.status)}
                        variant="secondary"
                      >
                        {systemHealth.status.toUpperCase()}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-2">
                        Last checked: {systemHealth.lastChecked.toLocaleTimeString()}
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base flex items-center gap-2">
                        <UserX className="h-4 w-4" />
                        Security Alerts
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Failed Logins (1h)</span>
                          <Badge variant={systemHealth.recentFailedLogins > 10 ? "destructive" : "secondary"}>
                            {systemHealth.recentFailedLogins}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Pending Invitations</span>
                          <Badge variant={systemHealth.pendingInvitations > 5 ? "destructive" : "secondary"}>
                            {systemHealth.pendingInvitations}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
