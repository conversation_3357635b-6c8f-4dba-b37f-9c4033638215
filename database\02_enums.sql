-- =============================================================================
-- FSNC Dashboard Database Setup - Custom Enum Types
-- =============================================================================
-- This script creates all custom enum types used throughout the database.
-- These enums ensure data consistency and provide type safety.
--
-- Run this script after 01_extensions_and_setup.sql
-- =============================================================================

-- =============================================================================
-- USER ROLE ENUM
-- =============================================================================

-- Define user roles with hierarchical permissions
-- admin > publisher > editor > member
CREATE TYPE user_role AS ENUM (
  'admin',      -- Full system access, user management, settings
  'publisher',  -- Content creation, editing, publishing, category/tag management
  'editor',     -- Content creation and editing, submit for review
  'member'      -- Basic authenticated access, read-only for most content
);

COMMENT ON TYPE user_role IS 'Hierarchical user roles defining system permissions';

-- =============================================================================
-- CONTENT STATUS ENUM
-- =============================================================================

-- Define content lifecycle states for posts and pages
CREATE TYPE content_status AS ENUM (
  'draft',          -- Work in progress, not visible to public
  'pending_review', -- Submitted for review by editor
  'rejected',       -- Rejected during review process
  'published',      -- Live and visible to public
  'archived'        -- Previously published but now hidden
);

COMMENT ON TYPE content_status IS 'Content lifecycle states for posts and pages';

-- =============================================================================
-- AUDIT ACTION ENUM
-- =============================================================================

-- Define types of actions that can be logged in the audit trail
CREATE TYPE audit_action AS ENUM (
  'INSERT',                      -- Record creation
  'UPDATE',                      -- Record modification
  'DELETE',                      -- Record deletion (including soft deletes)
  'LOGIN_SUCCESS',               -- Successful user authentication
  'LOGIN_FAIL',                  -- Failed authentication attempt
  'INVITE_SENT',                 -- User invitation sent
  'INVITE_ACCEPTED',             -- User invitation accepted
  'NEWSLETTER_SUBSCRIBE_ATTEMPT', -- Newsletter subscription attempt
  'NEWSLETTER_CONFIRMED',        -- Newsletter subscription confirmed
  'NEWSLETTER_UNSUBSCRIBE'       -- Newsletter unsubscription
);

COMMENT ON TYPE audit_action IS 'Types of actions tracked in the audit log';

-- =============================================================================
-- INVITATION STATUS ENUM
-- =============================================================================

-- Define invitation lifecycle states
CREATE TYPE invitation_status AS ENUM (
  'pending',    -- Invitation sent, awaiting response
  'accepted',   -- Invitation accepted, user registered
  'expired',    -- Invitation expired without acceptance
  'revoked'     -- Invitation manually cancelled
);

COMMENT ON TYPE invitation_status IS 'Invitation lifecycle states';

-- =============================================================================
-- NEWSLETTER SUBSCRIPTION STATUS ENUM
-- =============================================================================

-- Define newsletter subscription states
CREATE TYPE subscription_status AS ENUM (
  'pending',    -- Subscription requested, awaiting confirmation
  'active',     -- Confirmed and active subscription
  'unsubscribed', -- User unsubscribed
  'bounced',    -- Email bounced
  'complained'  -- User marked as spam
);

COMMENT ON TYPE subscription_status IS 'Newsletter subscription states';

-- =============================================================================
-- CONTENT VISIBILITY ENUM
-- =============================================================================

-- Define content visibility levels
CREATE TYPE content_visibility AS ENUM (
  'public',     -- Visible to everyone
  'private',    -- Visible only to authenticated users
  'restricted'  -- Visible only to specific roles
);

COMMENT ON TYPE content_visibility IS 'Content visibility levels';

-- =============================================================================
-- MEDIA TYPE ENUM
-- =============================================================================

-- Define media/file types for storage management
CREATE TYPE media_type AS ENUM (
  'image',      -- Image files (jpg, png, gif, webp, etc.)
  'document',   -- Document files (pdf, doc, etc.)
  'video',      -- Video files
  'audio',      -- Audio files
  'other'       -- Other file types
);

COMMENT ON TYPE media_type IS 'Media file type categories';

-- =============================================================================
-- NOTIFICATION TYPE ENUM
-- =============================================================================

-- Define notification types for the system
CREATE TYPE notification_type AS ENUM (
  'info',       -- Informational notifications
  'warning',    -- Warning notifications
  'error',      -- Error notifications
  'success'     -- Success notifications
);

COMMENT ON TYPE notification_type IS 'System notification types';

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Custom enum types created successfully at %', NOW();
END $$;
