'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { PostEditorForm } from '@/components/editor';
import { usePostEditor } from '@/hooks/use-post-editor';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>riangle, ArrowLeft } from 'lucide-react';

export default function EditPostPage() {
  const params = useParams();
  const router = useRouter();
  const postId = params.id as string;

  const {
    formData,
    originalPost,
    categories,
    authors,
    tags,
    loading,
    errors,
    isDirty,
    lastSaved,
    updateFormData,
    savePost,
    createTag,
    isLoading,
  } = usePostEditor(postId);

  const handleSave = async () => {
    const result = await savePost();
    return result;
  };

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="space-y-4">
          <div className="h-8 w-48 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-96 bg-muted animate-pulse rounded"></div>
        </div>
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <div className="h-96 bg-muted animate-pulse rounded"></div>
          </div>
          <div className="space-y-6">
            <div className="h-32 bg-muted animate-pulse rounded"></div>
            <div className="h-32 bg-muted animate-pulse rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (errors.post) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Failed to load post:</p>
              <p>{errors.post}</p>
              <Button variant="outline" onClick={() => router.push('/posts')}>
                Return to Posts
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const hasErrors = Object.values(errors).some(Boolean);

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        {originalPost && (
          <div className="text-sm text-muted-foreground">
            Editing: <span className="font-medium">{originalPost.title}</span>
          </div>
        )}
      </div>

      {hasErrors && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Failed to load editor data:</p>
              <ul className="list-disc list-inside text-sm">
                {Object.entries(errors).map(
                  ([key, error]) =>
                    error && (
                      <li key={key}>
                        {key}:{' '}
                        {typeof error === 'string'
                          ? error
                          : JSON.stringify(error)}
                      </li>
                    )
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <PostEditorForm
        formData={formData}
        categories={categories}
        authors={authors}
        tags={tags}
        onFormDataChange={updateFormData}
        onSave={handleSave}
        onCreateTag={createTag}
        isLoading={loading.saving}
        isSaving={loading.saving}
        isDirty={isDirty}
        lastSaved={lastSaved}
        errors={errors.validation}
        isEditing={true}
      />
    </div>
  );
}
