'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Users, 
  FolderOpen, 
  Tags, 
  Hash, 
  UserPlus,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit
} from 'lucide-react';
import type { DashboardStats } from '@/lib/dashboard-helpers';

interface DashboardStatsProps {
  stats: DashboardStats;
  isLoading?: boolean;
}

export function DashboardStatsComponent({ stats, isLoading }: DashboardStatsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted rounded"></div>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted rounded mb-2"></div>
              <div className="h-3 w-24 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const contentProgress = stats.totalPosts > 0 
    ? (stats.publishedPosts / stats.totalPosts) * 100 
    : 0;

  const statsCards = [
    {
      title: "Total Posts",
      value: stats.totalPosts,
      description: `${stats.publishedPosts} published`,
      icon: FileText,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      action: { label: "New Post", icon: Plus, href: "/posts/new" }
    },
    {
      title: "Total Pages",
      value: stats.totalPages,
      description: "Static content pages",
      icon: FolderOpen,
      color: "text-green-600",
      bgColor: "bg-green-50",
      action: { label: "New Page", icon: Plus, href: "/web-pages/new" }
    },
    {
      title: "Users",
      value: stats.totalUsers,
      description: "Registered users",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      action: { label: "Invite User", icon: UserPlus, href: "/user-management/invitations" }
    },
    {
      title: "Categories",
      value: stats.totalCategories,
      description: "Content categories",
      icon: Hash,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      action: { label: "Manage", icon: Edit, href: "/content-management/categories" }
    },
    {
      title: "Tags",
      value: stats.totalTags,
      description: "Content tags",
      icon: Tags,
      color: "text-pink-600",
      bgColor: "bg-pink-50",
      action: { label: "Manage", icon: Edit, href: "/content-management/tags" }
    },
    {
      title: "Pending Invitations",
      value: stats.pendingInvitations,
      description: "Awaiting response",
      icon: UserPlus,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      action: { label: "View All", icon: Users, href: "/user-management/invitations" }
    },
    {
      title: "Draft Posts",
      value: stats.draftPosts,
      description: "Unpublished content",
      icon: Clock,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
      action: { label: "Review", icon: Edit, href: "/posts?status=draft" }
    },
    {
      title: "Pending Review",
      value: stats.pendingReviewPosts,
      description: "Awaiting approval",
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
      action: { label: "Review", icon: CheckCircle, href: "/posts?status=pending_review" }
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Statistics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statsCards.map((stat, index) => {
          const Icon = stat.icon;
          const ActionIcon = stat.action.icon;
          
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mb-3">
                  {stat.description}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  asChild
                >
                  <a href={stat.action.href} className="flex items-center gap-2">
                    <ActionIcon className="h-3 w-3" />
                    {stat.action.label}
                  </a>
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Content Progress Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Content Progress
            </CardTitle>
            <CardDescription>
              Publishing progress and content status
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Published Content</span>
                <span>{Math.round(contentProgress)}%</span>
              </div>
              <Progress value={contentProgress} className="h-2" />
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">{stats.publishedPosts}</div>
                <div className="text-xs text-muted-foreground">Published</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">{stats.draftPosts}</div>
                <div className="text-xs text-muted-foreground">Drafts</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">{stats.pendingReviewPosts}</div>
                <div className="text-xs text-muted-foreground">Pending</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              User Management
            </CardTitle>
            <CardDescription>
              User statistics and pending actions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Users</span>
              <Badge variant="secondary">{stats.totalUsers}</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Pending Invitations</span>
              <Badge variant={stats.pendingInvitations > 0 ? "destructive" : "secondary"}>
                {stats.pendingInvitations}
              </Badge>
            </div>

            <Button className="w-full" asChild>
              <a href="/user-management/invitations" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Manage Invitations
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
