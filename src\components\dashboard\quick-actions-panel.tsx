'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  FileText, 
  FolderOpen, 
  UserPlus, 
  Hash, 
  Tags, 
  Download, 
  Upload, 
  Settings, 
  Trash2, 
  Copy, 
  Search,
  Zap,
  Wrench,
  Database,
  Shield,
  BarChart3
} from 'lucide-react';
import { useState } from 'react';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  category: 'content' | 'user' | 'system' | 'export';
  badge?: string;
  disabled?: boolean;
}

interface QuickActionsPanelProps {
  onActionClick?: (actionId: string) => void;
}

export function QuickActionsPanel({ onActionClick }: QuickActionsPanelProps) {
  const [isCommandOpen, setIsCommandOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const quickActions: QuickAction[] = [
    // Content Creation
    {
      id: 'new-post',
      title: 'New Post',
      description: 'Create a new blog post',
      icon: FileText,
      href: '/posts/new',
      category: 'content'
    },
    {
      id: 'new-page',
      title: 'New Page',
      description: 'Create a new static page',
      icon: FolderOpen,
      href: '/web-pages/new',
      category: 'content'
    },
    {
      id: 'new-category',
      title: 'New Category',
      description: 'Add content category',
      icon: Hash,
      href: '/content-management/categories/new',
      category: 'content'
    },
    {
      id: 'new-tag',
      title: 'New Tag',
      description: 'Create content tag',
      icon: Tags,
      href: '/content-management/tags/new',
      category: 'content'
    },

    // User Management
    {
      id: 'invite-user',
      title: 'Invite User',
      description: 'Send user invitation',
      icon: UserPlus,
      href: '/user-management/invitations/new',
      category: 'user'
    },
    {
      id: 'manage-profiles',
      title: 'Manage Profiles',
      description: 'View and edit user profiles',
      icon: Settings,
      href: '/user-management/profiles',
      category: 'user'
    },

    // System Operations
    {
      id: 'system-settings',
      title: 'System Settings',
      description: 'Configure application settings',
      icon: Settings,
      href: '/settings',
      category: 'system'
    },
    {
      id: 'audit-log',
      title: 'Audit Log',
      description: 'View system activity log',
      icon: Shield,
      href: '/audit-log',
      category: 'system'
    },
    {
      id: 'database-backup',
      title: 'Database Backup',
      description: 'Create system backup',
      icon: Database,
      category: 'system',
      onClick: () => alert('Backup functionality would be implemented here'),
      badge: 'Pro'
    },

    // Export & Analytics
    {
      id: 'export-posts',
      title: 'Export Posts',
      description: 'Download posts as CSV/JSON',
      icon: Download,
      category: 'export',
      onClick: () => alert('Export functionality would be implemented here')
    },
    {
      id: 'analytics-report',
      title: 'Analytics Report',
      description: 'Generate content analytics',
      icon: BarChart3,
      category: 'export',
      onClick: () => alert('Analytics report would be generated here'),
      badge: 'New'
    },
    {
      id: 'bulk-operations',
      title: 'Bulk Operations',
      description: 'Perform bulk content operations',
      icon: Copy,
      category: 'system',
      onClick: () => alert('Bulk operations panel would open here')
    }
  ];

  const categories = [
    { id: 'all', label: 'All Actions', icon: Zap },
    { id: 'content', label: 'Content', icon: FileText },
    { id: 'user', label: 'Users', icon: UserPlus },
    { id: 'system', label: 'System', icon: Wrench },
    { id: 'export', label: 'Export', icon: Download }
  ];

  const filteredActions = selectedCategory === 'all' 
    ? quickActions 
    : quickActions.filter(action => action.category === selectedCategory);

  const handleActionClick = (action: QuickAction) => {
    if (action.onClick) {
      action.onClick();
    } else if (action.href) {
      window.location.href = action.href;
    }
    
    if (onActionClick) {
      onActionClick(action.id);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              One-click shortcuts for common tasks
            </CardDescription>
          </div>
          
          <Dialog open={isCommandOpen} onOpenChange={setIsCommandOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Search Actions
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Quick Actions</DialogTitle>
                <DialogDescription>
                  Search and execute actions quickly
                </DialogDescription>
              </DialogHeader>
              <Command>
                <CommandInput placeholder="Search actions..." />
                <CommandList>
                  <CommandEmpty>No actions found.</CommandEmpty>
                  {categories.slice(1).map(category => {
                    const categoryActions = quickActions.filter(action => action.category === category.id);
                    if (categoryActions.length === 0) return null;
                    
                    return (
                      <CommandGroup key={category.id} heading={category.label}>
                        {categoryActions.map(action => {
                          const Icon = action.icon;
                          return (
                            <CommandItem
                              key={action.id}
                              onSelect={() => {
                                handleActionClick(action);
                                setIsCommandOpen(false);
                              }}
                              disabled={action.disabled}
                            >
                              <Icon className="mr-2 h-4 w-4" />
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <span>{action.title}</span>
                                  {action.badge && (
                                    <Badge variant="secondary" className="text-xs">
                                      {action.badge}
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {action.description}
                                </p>
                              </div>
                            </CommandItem>
                          );
                        })}
                      </CommandGroup>
                    );
                  })}
                </CommandList>
              </Command>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          {categories.map(category => {
            const Icon = category.icon;
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                <Icon className="h-3 w-3" />
                {category.label}
              </Button>
            );
          })}
        </div>
        
        <Separator />
        
        {/* Action Grid */}
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
          {filteredActions.map(action => {
            const Icon = action.icon;
            
            return (
              <Button
                key={action.id}
                variant="outline"
                className="h-auto p-4 flex flex-col items-start gap-2 hover:bg-muted/50"
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
              >
                <div className="flex items-center justify-between w-full">
                  <Icon className="h-5 w-5" />
                  {action.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {action.badge}
                    </Badge>
                  )}
                </div>
                <div className="text-left">
                  <div className="font-medium text-sm">{action.title}</div>
                  <div className="text-xs text-muted-foreground">
                    {action.description}
                  </div>
                </div>
              </Button>
            );
          })}
        </div>
        
        {filteredActions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No actions available in this category</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
