-- =============================================================================
-- FSNC Dashboard Database Setup - Performance Indexes
-- =============================================================================
-- This script creates additional indexes for performance optimization.
-- Basic indexes are already created in the table definitions.
--
-- Run this script after 06_rls_policies.sql
-- =============================================================================

-- =============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERIES
-- =============================================================================

-- Posts: Common filtering combinations
CREATE INDEX idx_posts_status_published_at ON posts(status, published_at DESC) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_posts_author_status ON posts(author_id, status) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_posts_category_status ON posts(category_id, status) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_posts_status_created_at ON posts(status, created_at DESC) 
  WHERE deleted_at IS NULL;

-- Pages: Common filtering combinations
CREATE INDEX idx_pages_status_updated_at ON pages(status, updated_at DESC) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_pages_author_status ON pages(author_id, status) 
  WHERE deleted_at IS NULL;

-- Biographies: Display order and visibility
CREATE INDEX idx_biographies_public_display_order ON biographies(is_public, display_order) 
  WHERE deleted_at IS NULL;

-- Invitations: Status and expiration tracking
CREATE INDEX idx_invitations_status_expires_at ON invitations(status, expires_at);

CREATE INDEX idx_invitations_email_status ON invitations(email, status);

-- Newsletter subscribers: Active status and confirmation
CREATE INDEX idx_newsletter_active_created_at ON newsletter_subscribers(is_active, created_at DESC);

CREATE INDEX idx_newsletter_confirmation_expires ON newsletter_subscribers(confirmation_token_expires_at) 
  WHERE confirmation_token IS NOT NULL;

-- =============================================================================
-- FULL-TEXT SEARCH INDEXES
-- =============================================================================

-- Posts: Full-text search on title and content
CREATE INDEX idx_posts_title_search ON posts USING gin(to_tsvector('english', title)) 
  WHERE deleted_at IS NULL;

-- Note: For content search, we'd need to extract text from JSONB
-- This is a simplified version - in production, you might want to store
-- a separate searchable_content column with plain text
CREATE INDEX idx_posts_content_search ON posts USING gin(to_tsvector('english', 
  COALESCE(content->>'text', content->>'content', ''))) 
  WHERE deleted_at IS NULL AND content IS NOT NULL;

-- Combined title and excerpt search
CREATE INDEX idx_posts_title_excerpt_search ON posts USING gin(
  to_tsvector('english', title || ' ' || COALESCE(excerpt, ''))
) WHERE deleted_at IS NULL;

-- Pages: Full-text search
CREATE INDEX idx_pages_title_search ON pages USING gin(to_tsvector('english', title)) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_pages_content_search ON pages USING gin(to_tsvector('english', 
  COALESCE(content->>'text', content->>'content', ''))) 
  WHERE deleted_at IS NULL AND content IS NOT NULL;

-- Categories and Tags: Name search
CREATE INDEX idx_categories_name_search ON categories USING gin(to_tsvector('english', name)) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_tags_name_search ON tags USING gin(to_tsvector('english', name)) 
  WHERE deleted_at IS NULL;

-- Biographies: Name and bio search
CREATE INDEX idx_biographies_name_search ON biographies USING gin(
  to_tsvector('english', full_name || ' ' || COALESCE(title_role, ''))
) WHERE deleted_at IS NULL;

-- =============================================================================
-- JSONB INDEXES FOR STRUCTURED DATA
-- =============================================================================

-- Posts: Content structure indexes (for rich text editor data)
CREATE INDEX idx_posts_content_type ON posts USING gin((content->'type')) 
  WHERE deleted_at IS NULL AND content IS NOT NULL;

-- Biographies: Social links indexes
CREATE INDEX idx_biographies_social_links ON biographies USING gin(social_links) 
  WHERE deleted_at IS NULL AND social_links IS NOT NULL;

-- Organization settings: Value indexes for common lookups
CREATE INDEX idx_organization_settings_value ON organization_settings USING gin(value);

-- Audit log: Old and new value indexes for change tracking
CREATE INDEX idx_audit_log_old_value ON audit_log USING gin(old_value) 
  WHERE old_value IS NOT NULL;

CREATE INDEX idx_audit_log_new_value ON audit_log USING gin(new_value) 
  WHERE new_value IS NOT NULL;

-- =============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- =============================================================================

-- Active/published content only
CREATE INDEX idx_posts_published_only ON posts(published_at DESC, title) 
  WHERE status = 'published' AND deleted_at IS NULL;

CREATE INDEX idx_pages_published_only ON pages(updated_at DESC, title) 
  WHERE status = 'published' AND deleted_at IS NULL;

-- Draft content for editors
CREATE INDEX idx_posts_drafts_by_author ON posts(author_id, updated_at DESC) 
  WHERE status = 'draft' AND deleted_at IS NULL;

CREATE INDEX idx_pages_drafts_by_author ON pages(author_id, updated_at DESC) 
  WHERE status = 'draft' AND deleted_at IS NULL;

-- Pending review content
CREATE INDEX idx_posts_pending_review ON posts(created_at DESC) 
  WHERE status = 'pending_review' AND deleted_at IS NULL;

CREATE INDEX idx_pages_pending_review ON pages(created_at DESC) 
  WHERE status = 'pending_review' AND deleted_at IS NULL;

-- Active newsletter subscribers only
CREATE INDEX idx_newsletter_active_subscribers ON newsletter_subscribers(email, created_at DESC) 
  WHERE is_active = true;

-- Pending invitations only
CREATE INDEX idx_invitations_pending ON invitations(created_at DESC) 
  WHERE status = 'pending';

-- Public biographies only
CREATE INDEX idx_biographies_public_only ON biographies(display_order, full_name) 
  WHERE is_public = true AND deleted_at IS NULL;

-- =============================================================================
-- TRIGRAM INDEXES FOR FUZZY SEARCH
-- =============================================================================

-- Enable fuzzy search on names and titles
CREATE INDEX idx_posts_title_trigram ON posts USING gin(title gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_pages_title_trigram ON pages USING gin(title gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_categories_name_trigram ON categories USING gin(name gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_tags_name_trigram ON tags USING gin(name gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_biographies_name_trigram ON biographies USING gin(full_name gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_profiles_name_trigram ON profiles USING gin(full_name gin_trgm_ops) 
  WHERE deleted_at IS NULL;

-- Email search for admin user management
CREATE INDEX idx_profiles_email_trigram ON profiles USING gin(email gin_trgm_ops) 
  WHERE deleted_at IS NULL;

CREATE INDEX idx_newsletter_email_trigram ON newsletter_subscribers USING gin(email gin_trgm_ops);

-- =============================================================================
-- COVERING INDEXES FOR COMMON QUERIES
-- =============================================================================

-- Posts list with essential data
CREATE INDEX idx_posts_list_covering ON posts(status, published_at DESC) 
  INCLUDE (id, title, slug, excerpt, author_id, category_id, created_at, updated_at)
  WHERE deleted_at IS NULL;

-- Pages list with essential data
CREATE INDEX idx_pages_list_covering ON pages(status, updated_at DESC) 
  INCLUDE (id, title, slug, author_id, created_at)
  WHERE deleted_at IS NULL;

-- User profiles with role information
CREATE INDEX idx_profiles_list_covering ON profiles(role, created_at DESC) 
  INCLUDE (id, email, full_name, avatar_url, updated_at)
  WHERE deleted_at IS NULL;

-- =============================================================================
-- MAINTENANCE INDEXES
-- =============================================================================

-- For cleanup operations
CREATE INDEX idx_invitations_cleanup ON invitations(expires_at, status) 
  WHERE status = 'pending';

CREATE INDEX idx_newsletter_cleanup ON newsletter_subscribers(confirmation_token_expires_at) 
  WHERE confirmation_token IS NOT NULL AND is_active = false;

-- For audit log archival (by date)
CREATE INDEX idx_audit_log_archival ON audit_log(created_at) 
  WHERE created_at < (NOW() - INTERVAL '1 year');

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Performance indexes created successfully at %', NOW();
END $$;
