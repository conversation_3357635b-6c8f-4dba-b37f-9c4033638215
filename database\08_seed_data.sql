-- =============================================================================
-- FSNC Dashboard Database Setup - Seed Data
-- =============================================================================
-- This script inserts initial/default data needed for the application to function.
-- This includes default organization settings, sample categories, and other
-- essential data.
--
-- Run this script after 07_indexes.sql
-- =============================================================================

-- =============================================================================
-- ORGANIZATION SETTINGS
-- =============================================================================

-- Insert default organization settings
INSERT INTO organization_settings (key, value, description) VALUES
  ('organization_name', '"FSNC Organization"', 'The name of the organization'),
  ('organization_description', '"A modern organization management platform"', 'Brief description of the organization'),
  ('contact_email', '"<EMAIL>"', 'Primary contact email address'),
  ('website_url', '"https://fsnc.org"', 'Public website URL'),
  ('logo_url', '""', 'Organization logo URL'),
  ('favicon_url', '""', 'Website favicon URL'),
  ('social_media', '{"twitter": "", "linkedin": "", "facebook": "", "instagram": ""}', 'Social media links'),
  ('newsletter_enabled', 'true', 'Whether newsletter functionality is enabled'),
  ('newsletter_double_optin', 'true', 'Whether newsletter requires double opt-in confirmation'),
  ('newsletter_from_email', '"<EMAIL>"', 'Newsletter sender email address'),
  ('newsletter_from_name', '"FSNC Newsletter"', 'Newsletter sender name'),
  ('max_file_upload_size', '10485760', 'Maximum file upload size in bytes (10MB)'),
  ('allowed_file_types', '["jpg", "jpeg", "png", "gif", "webp", "pdf", "doc", "docx"]', 'Allowed file upload types'),
  ('timezone', '"UTC"', 'Default timezone for the application'),
  ('date_format', '"YYYY-MM-DD"', 'Default date format'),
  ('time_format', '"HH:mm"', 'Default time format'),
  ('posts_per_page', '10', 'Number of posts to display per page'),
  ('enable_comments', 'false', 'Whether comments are enabled on posts'),
  ('enable_post_ratings', 'false', 'Whether post ratings are enabled'),
  ('maintenance_mode', 'false', 'Whether the site is in maintenance mode'),
  ('registration_enabled', 'false', 'Whether public registration is enabled (should be false for invite-only)'),
  ('email_verification_required', 'true', 'Whether email verification is required for new users'),
  ('session_timeout', '86400', 'Session timeout in seconds (24 hours)'),
  ('password_min_length', '8', 'Minimum password length requirement'),
  ('password_require_uppercase', 'true', 'Whether passwords must contain uppercase letters'),
  ('password_require_lowercase', 'true', 'Whether passwords must contain lowercase letters'),
  ('password_require_numbers', 'true', 'Whether passwords must contain numbers'),
  ('password_require_symbols', 'false', 'Whether passwords must contain symbols'),
  ('audit_log_retention_days', '365', 'Number of days to retain audit log entries'),
  ('backup_retention_days', '30', 'Number of days to retain database backups'),
  ('api_rate_limit_per_minute', '100', 'API rate limit per minute per user'),
  ('enable_api_access', 'true', 'Whether API access is enabled'),
  ('enable_webhooks', 'false', 'Whether webhook functionality is enabled'),
  ('webhook_secret', '""', 'Secret key for webhook verification'),
  ('smtp_host', '""', 'SMTP server host for email sending'),
  ('smtp_port', '587', 'SMTP server port'),
  ('smtp_username', '""', 'SMTP username'),
  ('smtp_password', '""', 'SMTP password (encrypted)'),
  ('smtp_encryption', '"tls"', 'SMTP encryption method (tls/ssl/none)'),
  ('google_analytics_id', '""', 'Google Analytics tracking ID'),
  ('google_tag_manager_id', '""', 'Google Tag Manager ID'),
  ('seo_meta_title', '"FSNC Dashboard"', 'Default SEO meta title'),
  ('seo_meta_description', '"Modern organization management and content platform"', 'Default SEO meta description'),
  ('seo_meta_keywords', '"organization, management, content, dashboard"', 'Default SEO meta keywords'),
  ('robots_txt', '"User-agent: *\nDisallow: /admin/\nDisallow: /api/"', 'Robots.txt content'),
  ('sitemap_enabled', 'true', 'Whether sitemap generation is enabled'),
  ('cache_enabled', 'true', 'Whether caching is enabled'),
  ('cache_ttl', '3600', 'Cache time-to-live in seconds (1 hour)'),
  ('cdn_enabled', 'false', 'Whether CDN is enabled for static assets'),
  ('cdn_url', '""', 'CDN base URL'),
  ('image_optimization_enabled', 'true', 'Whether image optimization is enabled'),
  ('image_max_width', '1920', 'Maximum image width in pixels'),
  ('image_max_height', '1080', 'Maximum image height in pixels'),
  ('image_quality', '85', 'Image compression quality (1-100)'),
  ('enable_search', 'true', 'Whether search functionality is enabled'),
  ('search_index_posts', 'true', 'Whether to index posts in search'),
  ('search_index_pages', 'true', 'Whether to index pages in search'),
  ('search_index_biographies', 'true', 'Whether to index biographies in search'),
  ('notification_email_enabled', 'true', 'Whether email notifications are enabled'),
  ('notification_new_user', 'true', 'Send notification when new user registers'),
  ('notification_new_post', 'true', 'Send notification when new post is created'),
  ('notification_post_published', 'true', 'Send notification when post is published'),
  ('notification_comment_posted', 'false', 'Send notification when comment is posted'),
  ('theme_primary_color', '"#3b82f6"', 'Primary theme color'),
  ('theme_secondary_color', '"#64748b"', 'Secondary theme color'),
  ('theme_accent_color', '"#10b981"', 'Accent theme color'),
  ('theme_dark_mode_enabled', 'true', 'Whether dark mode is available'),
  ('theme_default_mode', '"light"', 'Default theme mode (light/dark/auto)');

-- =============================================================================
-- DEFAULT CATEGORIES
-- =============================================================================

-- Insert default post categories
INSERT INTO categories (id, name, slug, description) VALUES
  (uuid_generate_v4(), 'General', 'general', 'General posts and announcements'),
  (uuid_generate_v4(), 'News', 'news', 'News and updates from the organization'),
  (uuid_generate_v4(), 'Events', 'events', 'Upcoming and past events'),
  (uuid_generate_v4(), 'Resources', 'resources', 'Helpful resources and guides'),
  (uuid_generate_v4(), 'Blog', 'blog', 'Blog posts and articles');

-- =============================================================================
-- DEFAULT TAGS
-- =============================================================================

-- Insert default tags
INSERT INTO tags (id, name, slug) VALUES
  (uuid_generate_v4(), 'Important', 'important'),
  (uuid_generate_v4(), 'Featured', 'featured'),
  (uuid_generate_v4(), 'Update', 'update'),
  (uuid_generate_v4(), 'Announcement', 'announcement'),
  (uuid_generate_v4(), 'Guide', 'guide'),
  (uuid_generate_v4(), 'Tutorial', 'tutorial'),
  (uuid_generate_v4(), 'Tips', 'tips'),
  (uuid_generate_v4(), 'Best Practices', 'best-practices'),
  (uuid_generate_v4(), 'Community', 'community'),
  (uuid_generate_v4(), 'Technology', 'technology');

-- =============================================================================
-- SAMPLE PAGES
-- =============================================================================

-- Insert default static pages
INSERT INTO pages (id, title, slug, content, status, published_at, meta_title, meta_description) VALUES
  (
    uuid_generate_v4(),
    'About Us',
    'about-us',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Welcome to our organization. This is a sample about page that you can customize with your organization''s information, mission, and values."}]}]}',
    'published',
    NOW(),
    'About Us - FSNC Organization',
    'Learn more about our organization, our mission, and our team.'
  ),
  (
    uuid_generate_v4(),
    'Contact',
    'contact',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Get in touch with us. This is a sample contact page where you can provide contact information, office locations, and contact forms."}]}]}',
    'published',
    NOW(),
    'Contact Us - FSNC Organization',
    'Contact our organization for inquiries, support, or collaboration opportunities.'
  ),
  (
    uuid_generate_v4(),
    'Privacy Policy',
    'privacy-policy',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This is a sample privacy policy page. Please customize this with your organization''s actual privacy policy and data handling practices."}]}]}',
    'published',
    NOW(),
    'Privacy Policy - FSNC Organization',
    'Our privacy policy and data protection practices.'
  ),
  (
    uuid_generate_v4(),
    'Terms of Service',
    'terms-of-service',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This is a sample terms of service page. Please customize this with your organization''s actual terms and conditions."}]}]}',
    'published',
    NOW(),
    'Terms of Service - FSNC Organization',
    'Terms and conditions for using our services and website.'
  );

-- =============================================================================
-- SAMPLE POSTS
-- =============================================================================

-- Insert a welcome post
INSERT INTO posts (id, title, slug, content, excerpt, status, published_at, category_id, meta_title, meta_description) VALUES
  (
    uuid_generate_v4(),
    'Welcome to FSNC Dashboard',
    'welcome-to-fsnc-dashboard',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Welcome to the FSNC Dashboard! This is your central hub for managing your organization''s content, users, and settings."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "This sample post demonstrates the rich text editing capabilities of the platform. You can create engaging content with various formatting options, images, and more."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Feel free to edit or delete this post and start creating your own content!"}]}]}',
    'Welcome to the FSNC Dashboard - your central hub for organization management.',
    'published',
    NOW(),
    (SELECT id FROM categories WHERE slug = 'general' LIMIT 1),
    'Welcome to FSNC Dashboard',
    'Get started with the FSNC Dashboard platform for organization management.'
  );

-- =============================================================================
-- SAMPLE BIOGRAPHY
-- =============================================================================

-- Insert a sample biography (this would typically be created by an admin)
INSERT INTO biographies (id, full_name, title_role, bio_text, is_public, display_order) VALUES
  (
    uuid_generate_v4(),
    'System Administrator',
    'Platform Administrator',
    '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This is a sample biography for the system administrator. You can customize this with actual team member information, including their background, experience, and role within the organization."}]}]}',
    true,
    1
  );

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Seed data inserted successfully at %', NOW();
  RAISE NOTICE 'Default categories created: %', (SELECT COUNT(*) FROM categories WHERE deleted_at IS NULL);
  RAISE NOTICE 'Default tags created: %', (SELECT COUNT(*) FROM tags WHERE deleted_at IS NULL);
  RAISE NOTICE 'Default pages created: %', (SELECT COUNT(*) FROM pages WHERE deleted_at IS NULL);
  RAISE NOTICE 'Organization settings configured: %', (SELECT COUNT(*) FROM organization_settings);
END $$;
