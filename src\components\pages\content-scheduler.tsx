'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  FileText, 
  Eye,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { format, isToday, isFuture, isPast } from 'date-fns';
import type { PageWithRelations, ContentStatus } from '@/types';

interface ScheduledContent {
  id: string;
  title: string;
  slug: string;
  status: ContentStatus;
  scheduledDate: Date;
  type: 'page' | 'post';
}

interface ContentSchedulerProps {
  pages: PageWithRelations[];
  onEdit: (pageId: string) => void;
  onPreview: (pageSlug: string) => void;
  onReschedule: (pageId: string, newDate: Date) => void;
}

export function ContentScheduler({ pages, onEdit, onPreview, onReschedule }: ContentSchedulerProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');

  // Convert pages to scheduled content format
  const scheduledContent: ScheduledContent[] = pages
    .filter(page => page.published_at)
    .map(page => ({
      id: page.id,
      title: page.title,
      slug: page.slug,
      status: page.status,
      scheduledDate: new Date(page.published_at!),
      type: 'page' as const,
    }));

  // Get content for selected date
  const getContentForDate = (date: Date) => {
    return scheduledContent.filter(content => 
      format(content.scheduledDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
    );
  };

  // Get upcoming content (next 7 days)
  const getUpcomingContent = () => {
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    return scheduledContent
      .filter(content => content.scheduledDate >= now && content.scheduledDate <= nextWeek)
      .sort((a, b) => a.scheduledDate.getTime() - b.scheduledDate.getTime());
  };

  // Get overdue content
  const getOverdueContent = () => {
    const now = new Date();
    return scheduledContent
      .filter(content => content.scheduledDate < now && content.status !== 'published')
      .sort((a, b) => a.scheduledDate.getTime() - b.scheduledDate.getTime());
  };

  const getStatusIcon = (status: ContentStatus) => {
    switch (status) {
      case 'published': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending_review': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'draft': return <FileText className="h-4 w-4 text-gray-600" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: ContentStatus) => {
    const variants = {
      draft: 'bg-gray-100 text-gray-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-red-100 text-red-800',
      rejected: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={variants[status] || variants.draft}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const getDateStatus = (date: Date, status: ContentStatus) => {
    if (status === 'published') return 'published';
    if (isPast(date)) return 'overdue';
    if (isToday(date)) return 'today';
    if (isFuture(date)) return 'scheduled';
    return 'unknown';
  };

  const selectedDateContent = getContentForDate(selectedDate);
  const upcomingContent = getUpcomingContent();
  const overdueContent = getOverdueContent();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Content Scheduler</h2>
          <p className="text-muted-foreground">
            Manage publication schedules and deadlines
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'calendar' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('calendar')}
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Calendar
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <FileText className="h-4 w-4 mr-2" />
            List
          </Button>
        </div>
      </div>

      {/* Alert for overdue content */}
      {overdueContent.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Overdue Content ({overdueContent.length})
            </CardTitle>
            <CardDescription className="text-red-700">
              The following content is past its scheduled publication date
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {overdueContent.slice(0, 3).map(content => (
                <div key={content.id} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(content.status)}
                    <span className="font-medium">{content.title}</span>
                    <span className="text-sm text-muted-foreground">
                      Due: {format(content.scheduledDate, 'MMM d, yyyy')}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" onClick={() => onEdit(content.id)}>
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
              {overdueContent.length > 3 && (
                <p className="text-sm text-red-700">
                  And {overdueContent.length - 3} more overdue items...
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {viewMode === 'calendar' ? (
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Calendar */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Publication Calendar</CardTitle>
              <CardDescription>
                Click on a date to see scheduled content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                className="rounded-md border"
                modifiers={{
                  hasContent: (date) => getContentForDate(date).length > 0,
                }}
                modifiersStyles={{
                  hasContent: { 
                    backgroundColor: 'rgb(59 130 246 / 0.1)',
                    color: 'rgb(59 130 246)',
                    fontWeight: 'bold'
                  },
                }}
              />
            </CardContent>
          </Card>

          {/* Selected Date Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="h-5 w-5" />
                {format(selectedDate, 'MMM d, yyyy')}
              </CardTitle>
              <CardDescription>
                {selectedDateContent.length} item(s) scheduled
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedDateContent.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <p>No content scheduled for this date</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedDateContent.map(content => (
                    <div key={content.id} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {getStatusIcon(content.status)}
                            <span className="font-medium truncate">{content.title}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {format(content.scheduledDate, 'h:mm a')}
                          </div>
                          <div className="mt-2">
                            {getStatusBadge(content.status)}
                          </div>
                        </div>
                        <div className="flex items-center gap-1 ml-2">
                          <Button size="sm" variant="ghost" onClick={() => onPreview(content.slug)}>
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="ghost" onClick={() => onEdit(content.id)}>
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        /* List View */
        <div className="space-y-6">
          {/* Upcoming Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Upcoming Content
              </CardTitle>
              <CardDescription>
                Content scheduled for the next 7 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              {upcomingContent.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No upcoming content</h3>
                  <p>No content is scheduled for the next 7 days.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {upcomingContent.map(content => (
                    <div key={content.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(content.status)}
                        <div>
                          <div className="font-medium">{content.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {format(content.scheduledDate, 'MMM d, yyyy \'at\' h:mm a')}
                          </div>
                        </div>
                        {getStatusBadge(content.status)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => onPreview(content.slug)}>
                          <Eye className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => onEdit(content.id)}>
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
