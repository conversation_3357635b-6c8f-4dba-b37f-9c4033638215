'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Layout, 
  FileText, 
  Globe, 
  Mail, 
  Users, 
  Plus,
  Eye,
  Edit,
  Copy
} from 'lucide-react';

interface PageTemplate {
  id: string;
  name: string;
  description: string;
  category: 'business' | 'marketing' | 'content' | 'custom';
  preview_url?: string;
  content: Record<string, any>;
  meta_title?: string;
  meta_description?: string;
  is_default?: boolean;
}

const defaultTemplates: PageTemplate[] = [
  {
    id: 'about',
    name: 'About Us',
    description: 'Professional about page template with team section and company story',
    category: 'business',
    content: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'About Our Company' }]
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Tell your company story here...' }]
        }
      ]
    },
    meta_title: 'About Us - Your Company Name',
    meta_description: 'Learn about our company, mission, and the team behind our success.',
    is_default: true,
  },
  {
    id: 'contact',
    name: 'Contact Us',
    description: 'Contact page with form, location, and contact information',
    category: 'business',
    content: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'Contact Us' }]
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Get in touch with our team...' }]
        }
      ]
    },
    meta_title: 'Contact Us - Your Company Name',
    meta_description: 'Contact our team for inquiries, support, or partnership opportunities.',
    is_default: true,
  },
  {
    id: 'landing',
    name: 'Landing Page',
    description: 'High-converting landing page with hero section and call-to-action',
    category: 'marketing',
    content: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'Welcome to Our Service' }]
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Compelling headline and value proposition...' }]
        }
      ]
    },
    meta_title: 'Welcome - Your Service Name',
    meta_description: 'Discover our amazing service and transform your business today.',
    is_default: true,
  },
  {
    id: 'privacy',
    name: 'Privacy Policy',
    description: 'Comprehensive privacy policy template with GDPR compliance',
    category: 'content',
    content: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'Privacy Policy' }]
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'This privacy policy explains how we collect and use your data...' }]
        }
      ]
    },
    meta_title: 'Privacy Policy - Your Company Name',
    meta_description: 'Our privacy policy explains how we protect and use your personal information.',
    is_default: true,
  },
];

interface PageTemplatesProps {
  onSelectTemplate: (template: PageTemplate) => void;
  onCreateFromTemplate: (template: PageTemplate) => void;
}

export function PageTemplates({ onSelectTemplate, onCreateFromTemplate }: PageTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredTemplates = selectedCategory === 'all' 
    ? defaultTemplates 
    : defaultTemplates.filter(template => template.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'business': return Users;
      case 'marketing': return Globe;
      case 'content': return FileText;
      case 'custom': return Layout;
      default: return Layout;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'business': return 'bg-blue-100 text-blue-800';
      case 'marketing': return 'bg-green-100 text-green-800';
      case 'content': return 'bg-purple-100 text-purple-800';
      case 'custom': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Page Templates</h2>
          <p className="text-muted-foreground">
            Choose from pre-built templates to quickly create new pages
          </p>
        </div>
        <Button variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Create Custom Template
        </Button>
      </div>

      {/* Category Filter */}
      <div className="flex items-center gap-2">
        <Button
          variant={selectedCategory === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedCategory('all')}
        >
          All Templates
        </Button>
        <Button
          variant={selectedCategory === 'business' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedCategory('business')}
        >
          <Users className="h-3 w-3 mr-1" />
          Business
        </Button>
        <Button
          variant={selectedCategory === 'marketing' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedCategory('marketing')}
        >
          <Globe className="h-3 w-3 mr-1" />
          Marketing
        </Button>
        <Button
          variant={selectedCategory === 'content' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedCategory('content')}
        >
          <FileText className="h-3 w-3 mr-1" />
          Content
        </Button>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => {
          const CategoryIcon = getCategoryIcon(template.category);
          
          return (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <CategoryIcon className="h-4 w-4" />
                      {template.name}
                      {template.is_default && (
                        <Badge variant="secondary" className="text-xs">Default</Badge>
                      )}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {template.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {/* Category Badge */}
                  <Badge className={getCategoryColor(template.category)}>
                    {template.category}
                  </Badge>

                  {/* Meta Info */}
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>Title: {template.meta_title}</div>
                    <div className="line-clamp-2">
                      Description: {template.meta_description}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onSelectTemplate(template)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => onCreateFromTemplate(template)}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Use Template
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-8">
          <Layout className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No templates found</h3>
          <p className="text-muted-foreground mb-4">
            No templates match your current filter.
          </p>
          <Button onClick={() => setSelectedCategory('all')}>
            Show All Templates
          </Button>
        </div>
      )}
    </div>
  );
}
