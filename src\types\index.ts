/**
 * Type definitions index
 * Centralized exports for all type definitions
 */

// Database types
export * from './database';

// Additional utility types for the application
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  category?: string;
  tag?: string;
  author?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface QueryParams extends PaginationParams, SortParams, FilterParams {}

// Form types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface SignUpFormData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
}

export interface InviteUserFormData {
  email: string;
  role: import('./database').UserRole;
  message?: string;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface ModalState {
  isOpen: boolean;
  data?: any;
}

// File upload types
export interface FileUploadState {
  files: File[];
  uploading: boolean;
  progress: number;
  error?: string;
  urls: string[];
}

// Editor types (for rich text content)
export interface EditorContent {
  type: 'doc';
  content: Array<{
    type: string;
    attrs?: Record<string, any>;
    content?: Array<{
      type: string;
      text?: string;
      marks?: Array<{
        type: string;
        attrs?: Record<string, any>;
      }>;
    }>;
  }>;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  badge?: string;
  children?: NavItem[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Search types
export interface SearchResult {
  id: string;
  title: string;
  type: 'post' | 'page' | 'category' | 'tag' | 'profile';
  url: string;
  excerpt?: string;
  image?: string;
  date?: Date;
}

// Analytics types
export interface AnalyticsData {
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: number;
  topPages: Array<{
    path: string;
    views: number;
    title?: string;
  }>;
  topReferrers: Array<{
    domain: string;
    visits: number;
  }>;
}

// Export commonly used database types for convenience
export type {
  AuditAction,
  ContentStatus,
  UserRole,
  Profile,
  Post,
  Page,
  Category,
  Tag,
  Biography,
  Invitation,
  NewsletterSubscriber,
  PostWithRelations,
  BiographyWithRelations,
  PageWithRelations,
  InvitationWithRelations,
  Database,
} from './database';
