/**
 * Dashboard helper functions for fetching analytics and statistics
 */

import { createClient } from './client';
import type {
  Post,
  Page,
  Profile,
  Category,
  Tag,
  AuditLog,
  Invitation,
  ContentStatus,
} from '@/types';

// =============================================================================
// DASHBOARD STATISTICS
// =============================================================================

export interface DashboardStats {
  totalPosts: number;
  totalPages: number;
  totalUsers: number;
  totalCategories: number;
  totalTags: number;
  pendingInvitations: number;
  publishedPosts: number;
  draftPosts: number;
  pendingReviewPosts: number;
}

export interface ContentStatusBreakdown {
  draft: number;
  pending_review: number;
  rejected: number;
  published: number;
  archived: number;
}

export interface RecentActivity {
  id: string;
  action: string;
  table_name: string;
  user_id?: string;
  user_name?: string;
  user_avatar?: string;
  created_at: Date;
  details?: Record<string, any>;
}

/**
 * Get comprehensive dashboard statistics
 */
export async function getDashboardStats(): Promise<{
  data?: DashboardStats;
  error?: string;
}> {
  try {
    const supabase = createClient();

    // Fetch all statistics in parallel
    const [
      postsResult,
      pagesResult,
      usersResult,
      categoriesResult,
      tagsResult,
      invitationsResult,
      postStatusResult,
    ] = await Promise.all([
      supabase
        .from('posts')
        .select('id', { count: 'exact', head: true })
        .is('deleted_at', null),
      supabase
        .from('pages')
        .select('id', { count: 'exact', head: true })
        .is('deleted_at', null),
      supabase
        .from('profiles')
        .select('id', { count: 'exact', head: true })
        .is('deleted_at', null),
      supabase
        .from('categories')
        .select('id', { count: 'exact', head: true })
        .is('deleted_at', null),
      supabase
        .from('tags')
        .select('id', { count: 'exact', head: true })
        .is('deleted_at', null),
      supabase
        .from('invitations')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'pending'),
      supabase.from('posts').select('status').is('deleted_at', null),
    ]);

    // Check for errors
    const errors = [
      postsResult.error,
      pagesResult.error,
      usersResult.error,
      categoriesResult.error,
      tagsResult.error,
      invitationsResult.error,
      postStatusResult.error,
    ].filter(Boolean);

    if (errors.length > 0) {
      return { error: `Failed to fetch statistics: ${errors[0]?.message}` };
    }

    // Calculate post status breakdown
    const postStatuses = postStatusResult.data || [];
    const publishedPosts = postStatuses.filter(
      (p) => p.status === 'published'
    ).length;
    const draftPosts = postStatuses.filter((p) => p.status === 'draft').length;
    const pendingReviewPosts = postStatuses.filter(
      (p) => p.status === 'pending_review'
    ).length;

    const stats: DashboardStats = {
      totalPosts: postsResult.count || 0,
      totalPages: pagesResult.count || 0,
      totalUsers: usersResult.count || 0,
      totalCategories: categoriesResult.count || 0,
      totalTags: tagsResult.count || 0,
      pendingInvitations: invitationsResult.count || 0,
      publishedPosts,
      draftPosts,
      pendingReviewPosts,
    };

    return { data: stats };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch dashboard statistics',
    };
  }
}

/**
 * Get content status breakdown for charts
 */
export async function getContentStatusBreakdown(): Promise<{
  data?: ContentStatusBreakdown;
  error?: string;
}> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('posts')
      .select('status')
      .is('deleted_at', null);

    if (error) {
      return { error: error.message };
    }

    const breakdown: ContentStatusBreakdown = {
      draft: 0,
      pending_review: 0,
      rejected: 0,
      published: 0,
      archived: 0,
    };

    data.forEach((post) => {
      if (post.status in breakdown) {
        breakdown[post.status as ContentStatus]++;
      }
    });

    return { data: breakdown };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch content status breakdown',
    };
  }
}

/**
 * Get recent activity from audit log
 */
export async function getRecentActivity(
  limit: number = 20
): Promise<{ data?: RecentActivity[]; error?: string }> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('audit_log')
      .select(
        `
        id,
        action,
        table_name,
        user_id,
        created_at,
        details,
        user:profiles!audit_log_user_id_fkey(full_name, avatar_url)
      `
      )
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      return { error: error.message };
    }

    const activities: RecentActivity[] = data.map((item) => ({
      id: item.id,
      action: item.action,
      table_name: item.table_name,
      user_id: item.user_id || undefined,
      user_name: item.user?.full_name || 'System',
      user_avatar: item.user?.avatar_url || undefined,
      created_at: new Date(item.created_at),
      details: item.details || undefined,
    }));

    return { data: activities };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch recent activity',
    };
  }
}

/**
 * Get upcoming scheduled posts
 */
export async function getUpcomingPosts(
  limit: number = 10
): Promise<{ data?: Post[]; error?: string }> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('posts')
      .select(
        `
        id,
        title,
        slug,
        published_at,
        status,
        author:profiles!posts_author_id_fkey(full_name, avatar_url)
      `
      )
      .eq('status', 'published')
      .gte('published_at', new Date().toISOString())
      .is('deleted_at', null)
      .order('published_at', { ascending: true })
      .limit(limit);

    if (error) {
      return { error: error.message };
    }

    return { data: data as Post[] };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch upcoming posts',
    };
  }
}

/**
 * Get user engagement metrics (simplified version)
 */
export async function getUserEngagementMetrics(): Promise<{
  data?: any;
  error?: string;
}> {
  try {
    const supabase = createClient();

    // Get user registration trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data, error } = await supabase
      .from('profiles')
      .select('created_at')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .is('deleted_at', null)
      .order('created_at', { ascending: true });

    if (error) {
      return { error: error.message };
    }

    // Group by day
    const dailyRegistrations = data.reduce(
      (acc: Record<string, number>, profile) => {
        const date = new Date(profile.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      },
      {}
    );

    const metrics = {
      totalNewUsers: data.length,
      dailyRegistrations,
      averageDaily: data.length / 30,
    };

    return { data: metrics };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch user engagement metrics',
    };
  }
}

/**
 * Get system health indicators
 */
export async function getSystemHealth(): Promise<{
  data?: any;
  error?: string;
}> {
  try {
    const supabase = createClient();

    // Check for recent errors in audit log
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    const { data: recentErrors, error: errorsError } = await supabase
      .from('audit_log')
      .select('id')
      .eq('action', 'LOGIN_FAIL')
      .gte('created_at', oneHourAgo.toISOString());

    if (errorsError) {
      return { error: errorsError.message };
    }

    // Get pending invitations count
    const { count: pendingInvites, error: invitesError } = await supabase
      .from('invitations')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (invitesError) {
      return { error: invitesError.message };
    }

    const health = {
      status: 'healthy' as 'healthy' | 'warning' | 'error',
      recentFailedLogins: recentErrors?.length || 0,
      pendingInvitations: pendingInvites || 0,
      lastChecked: new Date(),
    };

    // Determine overall health status
    if (health.recentFailedLogins > 10) {
      health.status = 'warning';
    }
    if (health.recentFailedLogins > 50) {
      health.status = 'error';
    }

    return { data: health };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch system health',
    };
  }
}
